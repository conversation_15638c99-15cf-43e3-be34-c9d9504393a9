/**
 * Agent Controller
 * 
 * Handles HTTP requests for agent operations.
 */

import { injectable, inject } from 'tsyringe';
import { BaseController } from '@/shared/utils/base-controller';
import { AgentService } from '../services/agent-service';
import { TYPES } from '@/shared/constants';
import { StatusCodes } from 'http-status-codes';
import type {
    CreateAgentRequestDto,
    UpdateAgentRequestDto,
    AgentListQueryDto,
    AgentResponseDto,
} from '../dto/agent-dto';
import { AuthenticatedRequest, TypedResponse } from '@/shared/types';

@injectable()
export class AgentController extends BaseController {
    constructor(@inject(TYPES.AgentService) private readonly agentService: AgentService) {
        super();
    }

    /**
     * Create a new agent
     */
    public createAgent = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<AgentResponseDto>): Promise<void> => {
            const createAgentDto = req.validatedBody as CreateAgentRequestDto;
            const agent = await this.agentService.createAgent(createAgentDto, this.getUserId(req));
            this.sendSuccess(res, agent, 'Agent created successfully', StatusCodes.CREATED);
        }
    );

    /**
     * Get all agents for the authenticated user
     */
    public listAgents = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<AgentResponseDto[]>): Promise<void> => {
            const query = req.validatedQuery as AgentListQueryDto;
            const result = await this.agentService.getAgents(query, this.getUserId(req));
            this.sendPaginatedResponse(res, result.agents, result.pagination, 'Agents retrieved successfully');
        }
    );

    /**
     * Get a single agent by ID
     */
    public getAgentById = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<AgentResponseDto>): Promise<void> => {
            const { agentId } = req.validatedParams as { agentId: string };
            const agent = await this.agentService.getAgentById(agentId, this.getUserId(req));
            this.sendSuccess(res, agent, 'Agent retrieved successfully');
        }
    );

    /**
     * Update an existing agent
     */
    public updateAgent = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<AgentResponseDto>): Promise<void> => {
            const { agentId } = req.validatedParams as { agentId: string };
            const updateAgentDto = req.validatedBody as UpdateAgentRequestDto;
            const agent = await this.agentService.updateAgent(agentId, updateAgentDto, this.getUserId(req));
            this.sendSuccess(res, agent, 'Agent updated successfully');
        }
    );

    /**
     * Delete an agent by ID
     */
    public deleteAgent = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<{ id: string }>): Promise<void> => {
            const { agentId } = req.validatedParams as { agentId: string };
            const result = await this.agentService.deleteAgent(agentId, this.getUserId(req));
            this.sendSuccess(res, result, 'Agent deleted successfully');
        }
    );
} 