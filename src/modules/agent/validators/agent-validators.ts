/**
 * Agent 验证器
 * 
 * 用于验证 agent 相关请求的 Express 中间件。
 */

import { commonSchemas } from '@/shared/utils/validator-helpers';
import {
    createParamsValidator,
    createBodyValidator,
    createQueryValidator
} from '@/shared/utils/validator-factory';
import {
    CreateAgentRequestSchema,
    UpdateAgentRequestSchema,
    AgentListQuerySchema,
} from '../dto/agent-dto';
import { z } from 'zod/v4';
import type { RequestHandler } from 'express';

/**
 * 使用通用工具预构建的验证器
 */
export const validateAgentId: RequestHandler = createParamsValidator(
    z.object({ agentId: commonSchemas.uuid }), 
    '无效的 agent ID'
);
export const validateCreateAgentRequest: RequestHandler = createBodyValidator(
    CreateAgentRequestSchema as unknown as z.ZodType, '创建 agent 请求'
);
export const validateUpdateAgentRequest: RequestHandler = createBodyValidator(
    UpdateAgentRequestSchema as unknown as z.ZodType, '更新 agent 请求'
);
export const validateAgentListQuery: RequestHandler = createQueryValidator(
    AgentListQuerySchema as unknown as z.ZodType, 'agent 列表查询'
);

/**
 * 不同操作的组合验证器数组
 */
export const validateCreateAgent: RequestHandler[] = [
    validateCreateAgentRequest,
];

export const validateUpdateAgent: RequestHandler[] = [
    validateAgentId,
    validateUpdateAgentRequest,
];

export const validateGetAgent: RequestHandler[] = [
    validateAgentId,
];

export const validateDeleteAgent: RequestHandler[] = [
    validateAgentId,
];

export const validateListAgents: RequestHandler[] = [
    validateAgentListQuery,
];