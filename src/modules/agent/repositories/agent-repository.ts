/**
 * Agent Repository
 * 
 * Handles database operations for agents with proper error handling and logging.
 */

import { injectable, inject } from 'tsyringe';
import { eq, and, desc, asc, count } from 'drizzle-orm';
import { databaseService } from '@/infrastructure/database';
import { agent } from '@/infrastructure/database/schema/agent';
import { logDatabaseOperation } from '@/infrastructure/logger';
import { measureTime } from '@/shared/utils/time';
import { SessionRepository, type SessionEntity } from '@/modules/session/repositories/session-repository';
import { TYPES } from '@/shared/constants';
import type { 
    Agent, 
    AgentInsert, 
    AgentUpdate, 
    AgentListQueryDto,
} from '../dto/agent-dto';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

/**
 * Agent with latest sessions interface
 */
export interface AgentWithSessions extends Agent {
    latestSessions?: SessionEntity[];
}

@injectable()
export class AgentRepository {
    constructor(
        @inject(TYPES.SessionRepository) private readonly sessionRepository: SessionRepository
    ) {}

    private get db(): PostgresJsDatabase<Record<string, unknown>> {
        return databaseService.getDb();
    }

    getDb(): PostgresJsDatabase<Record<string, unknown>> {
        return this.db;
    }

    /**
     * Create a new agent
     */
    async create(data: AgentInsert, userId: string): Promise<Agent> {
        const agentData: AgentInsert = {
            ...data,
            userId,
        };

        const { result: createdAgents, duration } = await measureTime(async () => {
            return await this.db
                .insert(agent)
                .values(agentData)
                .returning();
        });

        const newAgent = createdAgents[0];
        if (!newAgent) {
            throw new Error('Failed to create agent');
        }

        logDatabaseOperation('INSERT', 'agent', duration, {
            agentId: newAgent.id,
            userId,
        });

        return newAgent;
    }

    /**
     * Get agent by ID with user access control
     */
    async findById(id: string): Promise<Agent | null> {
        const conditions = [eq(agent.id, id)];
        
        const { result: agents, duration } = await measureTime(async () => {
            return await this.db
                .select()
                .from(agent)
                .where(and(...conditions))
                .limit(1);
        });

        logDatabaseOperation('SELECT', 'agent', duration, {
            agentId: id,
            found: agents.length > 0,
        });

        return agents[0] ?? null;
    }

    /**
     * Update agent with user access control
     */
    async update(id: string, data: Partial<AgentUpdate>, userId: string): Promise<Agent | null> {
        const { result: updatedAgents, duration } = await measureTime(async () => {
            return await this.db
                .update(agent)
                .set(data)
                .where(and(
                    eq(agent.id, id),
                ))
                .returning();
        });

        logDatabaseOperation('UPDATE', 'agent', duration, {
            agentId: id,
            userId,
            updated: updatedAgents.length > 0,
        });

        return updatedAgents[0] ?? null;
    }

    /**
     * Soft delete agent (set status to 0)
     */
    async delete(id: string, userId: string): Promise<boolean> {
        const { result: deletedAgents, duration } = await measureTime(async () => {
            return await this.db
                .update(agent)
                .set({ status: 0 }) // Soft delete
                .where(and(
                    eq(agent.id, id),
                    eq(agent.userId, userId)
                ))
                .returning();
        });

        logDatabaseOperation('UPDATE', 'agent', duration, {
            agentId: id,
            userId,
            deleted: deletedAgents.length > 0,
            operation: 'soft_delete',
        });

        return deletedAgents.length > 0;
    }

    /**
     * List agents with filtering and pagination
     * If userId is provided, includes latest 2 sessions for each agent
     */
    async list(query: AgentListQueryDto, userId?: string): Promise<{ agents: AgentWithSessions[]; total: number }> {
        const {
            page = 1,
            limit = 10,
            type,
            target,
            status,
            sortBy = 'createdAt',
            sortOrder = 'desc',
            includeDeleted = false,
        } = query;

        const offset = (page - 1) * limit;
        const conditions = [];

        // Filter by status
        if (status !== undefined) {
            conditions.push(eq(agent.status, status));
        } else if (!includeDeleted) {
            conditions.push(eq(agent.status, 1)); // Only active
        }

        // Filter by type
        if (type !== undefined) {
            conditions.push(eq(agent.type, type));
        }

        // Filter by target
        if (target !== undefined) {
            conditions.push(eq(agent.target, target));
        }

        const whereCondition = conditions.length > 0 ? and(...conditions) : undefined;

        // Get total count
        const { result: countResult, duration: countDuration } = await measureTime(async () => {
            return await this.db
                .select({ count: count() })
                .from(agent)
                .where(whereCondition);
        });

        const totalCount = countResult[0]?.count ?? 0;

        // Get agents with sorting
        const sortColumn = sortBy === 'name' ? agent.name :
                          sortBy === 'type' ? agent.type :
                          sortBy === 'updatedAt' ? agent.updatedAt :
                          agent.createdAt;

        const sortDirection = sortOrder === 'asc' ? asc(sortColumn) : desc(sortColumn);

        const { result: agents, duration: selectDuration } = await measureTime(async () => {
            return await this.db
                .select()
                .from(agent)
                .where(whereCondition)
                .orderBy(sortDirection)
                .limit(limit)
                .offset(offset);
        });

        // If userId is provided, get latest sessions for each agent
        let agentsWithSessions: AgentWithSessions[] = agents;
        
        if (userId !== undefined && userId !== '') {
            const { result: sessionsMap, duration: sessionsDuration } = await measureTime(async () => {
                const sessionsPromises = agents.map(async (agentItem) => {
                    const latestSessions = await this.sessionRepository.findLatestByAgentAndUser(agentItem.id, userId);
                    return { agentId: agentItem.id, sessions: latestSessions };
                });
                
                const sessionsResults = await Promise.all(sessionsPromises);
                const map = new Map<string, SessionEntity[]>();
                sessionsResults.forEach(result => {
                    map.set(result.agentId, result.sessions);
                });
                return map;
            });

            agentsWithSessions = agents.map(agentItem => ({
                ...agentItem,
                latestSessions: sessionsMap.get(agentItem.id) ?? []
            }));

            logDatabaseOperation('SELECT', 'agent_with_sessions', countDuration + selectDuration + sessionsDuration, {
                userId,
                total: totalCount,
                page,
                limit,
                count: agents.length,
                sessionsCount: Array.from(sessionsMap.values()).flat().length,
                operation: 'list_with_sessions',
            });
        } else {
            logDatabaseOperation('SELECT', 'agent', countDuration + selectDuration, {
                userId: userId ?? 'anonymous',
                total: totalCount,
                page,
                limit,
                count: agents.length,
                operation: 'list_with_count',
            });
        }

        return {
            agents: agentsWithSessions,
            total: totalCount,
        };
    }
} 