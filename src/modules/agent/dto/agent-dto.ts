/**
 * Agent Data Transfer Objects
 * 
 * DTOs that match the actual database schema.
 * Includes validation schemas for all agent endpoints.
 */

import { z } from 'zod/v4';

/**
 * Agent Type Enums
 */
export const AgentType = {
    CREATIVE_COLLABORATION: 1, // 创作协作类
    IMMERSIVE_TEACHING: 2,     // 沉浸式教学类
} as const;

export const AgentTarget = {
    STUDENT: 1,     // "01": 学员
    ADMIN: 2,       // "10": 管理员
    BOTH: 3,        // "11": 学员和管理员
} as const;

export const AgentStatus = {
    DELETED: 0,     // 已删除
    ACTIVE: 1,      // 正常
    INACTIVE: 2,    // 下架
} as const;

export const AgentGroup = {
    AI_TEACHER: 1,  // AI 老师
    AI_PRACTICE: 2, // AI 对练
    LEADER_MATE: 3, // Leader Mate
} as const;

/**
 * Base Agent Schema (matches database schema)
 */
export const AgentSchema = z.object({
    id: z.uuid(),
    name: z.string().min(1, { error: 'Name is required' }).max(255, { error: 'Name is too long' }),
    avatar: z.url({ message: 'Invalid URL format' }),
    cardUrl: z.url({ message: 'Invalid URL format' }),
    type: z.number().int().min(1, { error: 'Invalid agent type' }).max(2, { error: 'Invalid agent type' }),
    group: z.number().int().min(1, { error: 'Invalid agent group' }).max(3, { error: 'Invalid agent group' }),
    target: z.number().int().min(1, { error: 'Invalid agent target' }).max(3, { error: 'Invalid agent target' }),
    status: z.number().int().min(0, { error: 'Invalid agent status' }).max(2, { error: 'Invalid agent status' }),
    umdUrl: z.url({ message: 'Invalid URL format' }).nullable(),
    userId: z.uuid(),
    createdAt: z.date(),
    updatedAt: z.date(),
});

/**
 * Agent Insert Schema (for creating agents)
 */
export const AgentInsertSchema = AgentSchema.omit({
    id: true,
    createdAt: true,
    updatedAt: true,
});

/**
 * Agent Update Schema (for updating agents)
 */
export const AgentUpdateSchema = AgentInsertSchema.partial();

/**
 * Type definitions
 */
export type Agent = z.infer<typeof AgentSchema>;
export type AgentInsert = z.infer<typeof AgentInsertSchema>;
export type AgentUpdate = z.infer<typeof AgentUpdateSchema>;

/**
 * Create Agent Request DTO
 */
export const CreateAgentRequestSchema = z.object({
    name: z.string().min(1, { error: 'Name is required' }).max(255, { error: 'Name is too long' }),
    avatar: z.url({ message: 'Invalid URL format' }),
    cardUrl: z.url({ message: 'Invalid URL format' }),
    type: z.number().int().min(1, { error: 'Invalid agent type' }).max(2, { error: 'Invalid agent type' }),
    group: z.number().int().min(1, { error: 'Invalid agent group' }).max(3, { error: 'Invalid agent group' }),
    target: z.number().int().min(1, { error: 'Invalid agent target' }).max(3, { error: 'Invalid agent target' }),
    status: z.number().int().min(0, { error: 'Invalid agent status' }).max(2, { error: 'Invalid agent status' }),
    umdUrl: z.url({ message: 'Invalid URL format' }).nullish(),
});

export type CreateAgentRequestDto = z.infer<typeof CreateAgentRequestSchema>;

/**
 * Update Agent Request DTO
 */
export const UpdateAgentRequestSchema = CreateAgentRequestSchema.partial();

export type UpdateAgentRequestDto = z.infer<typeof UpdateAgentRequestSchema>;

/**
 * Agent Response DTO
 */
export const AgentResponseSchema = z.object({
    id: z.uuid(),
    name: z.string(),
    avatar: z.url({ message: 'Invalid URL format' }),
    cardUrl: z.url({ message: 'Invalid URL format' }),
    type: z.number().int(),
    typeDescription: z.string(),
    group: z.number().int(),
    groupDescription: z.string(),
    target: z.number().int(),
    targetDescription: z.string(),
    status: z.number().int(),
    statusDescription: z.string(),
    umdUrl: z.url({ message: 'Invalid URL format' }).nullable(),
    userId: z.uuid(),
    createdAt: z.string(),
    updatedAt: z.string(),
});

export type AgentResponseDto = z.infer<typeof AgentResponseSchema>;

/**
 * Agent List Query DTO
 */
export const AgentListQuerySchema = z.object({
    page: z.coerce.number().int().min(1).default(1),
    limit: z.coerce.number().int().min(1).max(100).default(10),
    type: z.coerce.number().int().min(1).max(2).optional(),
    group: z.coerce.number().int().min(1).max(3).optional(),
    target: z.coerce.number().int().min(1).max(3).optional(),
    status: z.coerce.number().int().min(0).max(2).optional(),
    sortBy: z.enum(['name', 'type', 'createdAt', 'updatedAt']).default('createdAt'),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
    includeDeleted: z.coerce.boolean().default(false),
});

export type AgentListQueryDto = z.infer<typeof AgentListQuerySchema>;

/**
 * Agent List Response DTO
 */
export const AgentListResponseSchema = z.object({
    agents: z.array(AgentResponseSchema),
    pagination: z.object({
        page: z.number().int(),
        limit: z.number().int(),
        total: z.number().int(),
        totalPages: z.number().int(),
        hasNext: z.boolean(),
        hasPrev: z.boolean(),
    }),
});

export type AgentListResponseDto = z.infer<typeof AgentListResponseSchema>;

/**
 * Helper functions to get descriptions
 */
export function getAgentTypeDescription(type: number): string {
    switch (type) {
        case AgentType.CREATIVE_COLLABORATION:
            return '创作协作类';
        case AgentType.IMMERSIVE_TEACHING:
            return '沉浸式教学类';
        default:
            return '未知类型';
    }
}

export function getAgentGroupDescription(group: number): string {
    switch (group) {
        case AgentGroup.AI_TEACHER:
            return 'AI 老师';
        case AgentGroup.AI_PRACTICE:
            return 'AI 对练';
        case AgentGroup.LEADER_MATE:
            return 'Leader Mate';
        default:
            return '未分组';
    }
}

export function getAgentTargetDescription(target: number): string {
    switch (target) {
        case AgentTarget.STUDENT:
            return '学员';
        case AgentTarget.ADMIN:
            return '管理员';
        case AgentTarget.BOTH:
            return '学员和管理员';
        default:
            return '未知目标';
    }
}

export function getAgentStatusDescription(status: number): string {
    switch (status) {
        case AgentStatus.ACTIVE:
            return '正常';
        case AgentStatus.INACTIVE:
            return '下架';
        case AgentStatus.DELETED:
            return '已删除';
        default:
            return '未知状态';
    }
} 