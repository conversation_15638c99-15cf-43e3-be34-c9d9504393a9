/**
 * Agent Service
 * 
 * Business logic layer for agent operations.
 * Handles agent creation, retrieval, update, and deletion with proper validation.
 */

import { injectable, inject } from 'tsyringe';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { AgentRepository } from '../repositories/agent-repository';
import { AgentMapper } from './agent-mapper';
import { NotFoundError, AuthorizationError, ValidationError } from '@/shared/utils/errors';
import { TYPES } from '@/shared/constants';
import { 
    AgentStatus,
    AgentGroup,
    CreateAgentRequestDto, 
    UpdateAgentRequestDto,
    AgentListQueryDto,
    AgentResponseDto,
    type AgentListResponseDto,
    type AgentInsert, 
    type AgentUpdate,
} from '../dto/agent-dto';

@injectable()
export class AgentService {
    constructor(
        @inject(TYPES.AgentRepository) private readonly agentRepository: AgentRepository,
        @inject(TYPES.Logger) private readonly logger: typeof Logger,
        @inject(TYPES.AgentMapper) private readonly agentMapper: AgentMapper
    ) {}

    /**
     * Get all agents with pagination and filtering
     */
    async getAgents(query: AgentListQueryDto, userId: string): Promise<AgentListResponseDto> {
        const { agents, total } = await this.agentRepository.list(query, userId);
        const agentDtos = this.agentMapper.toDtoList(agents);

        const totalPages = Math.ceil(total / query.limit);
        const pagination = {
            page: query.page,
            limit: query.limit,
            total,
            totalPages,
            hasNext: query.page < totalPages,
            hasPrev: query.page > 1,
        };

        this.logger.info('Agents retrieved successfully', { 
            userId,
            count: agentDtos.length,
            total: pagination.total,
            page: query.page,
            limit: query.limit,
        });

        return { agents: agentDtos, pagination };
    }

    /**
     * Get agent by ID with access control
     */
    async getAgentById(agentId: string, userId: string): Promise<AgentResponseDto> {
        if (!agentId) {
            throw new ValidationError('Agent ID are required');
        }
        
        const agent = await this.agentRepository.findById(agentId);
        
        if (!agent) {
            throw new NotFoundError('Agent not found');
        }

        this.logger.info('Agent retrieved successfully', {
            agentId,
            userId,
            agentName: agent.name,
        });

        return this.agentMapper.toDto(agent);
    }

    /**
     * Create new agent
     */
    async createAgent(data: CreateAgentRequestDto, userId: string): Promise<AgentResponseDto> {
        const agentToCreate: AgentInsert = {
            ...data,
            userId,
            status: AgentStatus.ACTIVE,
            group: data.group ?? AgentGroup.AI_TEACHER,
            umdUrl: data.umdUrl ?? null,
        };
        
        const agent = await this.agentRepository.create(agentToCreate, userId);

        this.logger.info('Agent created successfully', { 
            agentId: agent.id,
            userId,
            name: data.name,
            type: data.type,
            target: data.target,
        });
        
        return this.agentMapper.toDto(agent);
    }

    /**
     * Update agent with access control
     */
    async updateAgent(agentId: string, data: UpdateAgentRequestDto, userId: string): Promise<AgentResponseDto> {
        const existingAgent = await this.agentRepository.findById(agentId);
        if (!existingAgent) {
            throw new NotFoundError('Agent not found');
        }

        const agentToUpdate: AgentUpdate = { ...data };
        const updatedAgent = await this.agentRepository.update(agentId, agentToUpdate, userId);
        
        if (!updatedAgent) {
            throw new NotFoundError('Agent not found after update');
        }

        this.logger.info('Agent updated successfully', { 
            agentId,
            userId,
            updatedFields: Object.keys(data),
            agentName: updatedAgent.name,
        });
        
        return this.agentMapper.toDto(updatedAgent);
    }

    /**
     * Delete agent with access control
     */
    async deleteAgent(agentId: string, userId: string): Promise<{ id: string }> {        
        const existingAgent = await this.agentRepository.findById(agentId);
        if (!existingAgent) {
            throw new NotFoundError('Agent not found');
        }

        if (existingAgent.userId !== userId) {
            throw new AuthorizationError('User is not authorized to delete this agent');
        }

        const deleted = await this.agentRepository.delete(agentId, userId);
        
        if (!deleted) {
            throw new NotFoundError('Agent not found');
        }

        this.logger.info('Agent deleted successfully', { 
            agentId, 
            userId,
            agentName: existingAgent.name,
        });

        return { id: agentId };
    }
} 