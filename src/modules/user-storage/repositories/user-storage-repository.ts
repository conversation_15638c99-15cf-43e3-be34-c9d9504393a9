/**
 * User Storage Repository
 * 
 * Data access layer for user storage operations based on key.
 */

import { injectable } from 'tsyringe';
import { eq, and } from 'drizzle-orm';
import { 
    userStorage, 
    type UserStorage, 
    type InsertUserStorage, 
    type UpdateUserStorage 
} from '@/infrastructure/database/schema/user-storage';
import { databaseService } from '@/infrastructure/database';
import { logDatabaseOperation } from '@/infrastructure/logger';
import { measureTime } from '@/shared/utils/time';

import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

export interface UserStorageEntity extends UserStorage {}

export interface CreateUserStorageEntity extends Omit<InsertUserStorage, 'id' | 'createdAt' | 'updatedAt'> {}

export interface UpdateUserStorageEntity extends Partial<Omit<UpdateUserStorage, 'id' | 'userId' | 'createdAt'>> {}

export interface UserStorageListResult {
    items: UserStorageEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}

@injectable()
export class UserStorageRepository {
    private get db(): PostgresJsDatabase<Record<string, unknown>> {
        return databaseService.getDb();
    }

    /**
     * Get user storage by key
     */
    async findByKey(key: string, userId: string): Promise<UserStorageEntity | null> {
        const { result: storage, duration } = await measureTime(async () => {
            const result = await this.db
                .select()
                .from(userStorage)
                .where(and(
                    eq(userStorage.key, key),
                    eq(userStorage.userId, userId)
                ))
                .limit(1);
            
            return result[0] ?? null;
        });

        logDatabaseOperation('SELECT', 'user_storage', duration, {
            key,
            userId,
            found: !!storage,
        });

        return storage;
    }

    /**
     * Create user storage entry
     */
    async create(data: CreateUserStorageEntity): Promise<UserStorageEntity> {
        const { result: inserted, duration } = await measureTime(async () => {
            const [result] = await this.db
                .insert(userStorage)
                .values({
                    ...data,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                })
                .returning();
            
            return result;
        });

        if (!inserted) {
            throw new Error('Failed to create user storage entry');
        }

        logDatabaseOperation('INSERT', 'user_storage', duration, {
            storageId: inserted.id,
            userId: inserted.userId,
            key: inserted.key,
        });

        return inserted;
    }

    /**
     * Update user storage entry
     */
    async update(
        key: string,
        data: UpdateUserStorageEntity
    ): Promise<UserStorageEntity | null> {
        const { result: updated, duration } = await measureTime(async () => {
            const [result] = await this.db
                .update(userStorage)
                .set({
                    ...data,
                    updatedAt: new Date(),
                })
                .where(and(
                    eq(userStorage.key, key)
                ))
                .returning();

            return result ?? null;
        });
        
        if (updated) {
            logDatabaseOperation('UPDATE', 'user_storage', duration, {
                storageId: updated.id,
                key,
            });
        }

        return updated;
    }

    /**
     * Delete user storage entry by key
     */
    async deleteByKey(key: string, userId: string): Promise<boolean> {
        const { result: deletedCount, duration } = await measureTime(async () => {
            const result = await this.db
                .delete(userStorage)
                .where(and(
                    eq(userStorage.key, key),
                    eq(userStorage.userId, userId)
                ))
                .returning({ id: userStorage.id });
            
            return result.length;
        });

        logDatabaseOperation('DELETE', 'user_storage', duration, {
            key,
            userId,
            deleted: deletedCount > 0,
        });

        return deletedCount > 0;
    }
} 