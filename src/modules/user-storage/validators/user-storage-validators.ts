/**
 * User Storage Validators
 * 
 * Express middleware for validating user storage requests.
 */


import {
    createParamsValidator,
    createBodyValidator,
} from '@/shared/utils/validator-factory';
import {
    CreateOrUpdateUserStorageRequestSchema,
    UserStorageKeyParamsSchema,
} from '../dto/user-storage-dto';
import type { RequestHandler } from 'express';

/**
 * Pre-built validators using the generic factory functions
 */
export const validateUserStorageKey: RequestHandler = createParamsValidator(
    UserStorageKeyParamsSchema,
    'Invalid user storage key'
);

export const validateCreateUserStorageRequest: RequestHandler = createBodyValidator(
    CreateOrUpdateUserStorageRequestSchema,
    'Invalid create user storage request'
);

export const validateUpdateUserStorageRequest: RequestHandler = createBodyValidator(
    CreateOrUpdateUserStorageRequestSchema,
    'Invalid update user storage request'
);