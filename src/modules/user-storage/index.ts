/**
 * User Storage Module
 * 
 * This module handles all user storage functionality including:
 * - User storage management (CRUD operations based on key)
 * - JSON data storage and retrieval
 * - User-specific key-value storage
 * - Version control and expiration support
 */

// Export all module components
export * from './dto';
export * from './services';
export * from './controllers';
export * from './validators';

// Export repository types explicitly to avoid conflicts
export type { 
    UserStorageRepository,
    UserStorageEntity as RepositoryUserStorageEntity,
    CreateUserStorageEntity as RepositoryCreateUserStorageEntity,
    UpdateUserStorageEntity as RepositoryUpdateUserStorageEntity,
    UserStorageListResult
} from './repositories'; 