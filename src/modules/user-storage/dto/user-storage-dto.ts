/**
 * User Storage DTO
 * 
 * Data transfer objects for user storage operations.
 */

import { z } from 'zod/v4';
import { commonSchemas } from '@/shared/utils/validator-helpers';
import type { UserStorage } from '@/infrastructure/database/schema/user-storage';

/**
 * Base user storage data
 */
export interface UserStorageResponseDto {
    id: string;
    userId: string;
    key: string;
    value: unknown;
    description?: string;
    version: string;
    expiresAt?: Date;
    createdAt: string;
    updatedAt: string;
}

/**
 * Create or update user storage request
 */
export interface CreateOrUpdateUserStorageRequestDto {
    value?: unknown;
    description?: string;
    version?: string;
    expiresAt?: Date;
}


/**
 * Validation schemas
 */
export const CreateOrUpdateUserStorageRequestSchema = z.object({
    value: z.unknown(),
    description: z.string().max(500, '描述长度不能超过500个字符').optional(),
    version: z.string().max(50, '版本号长度不能超过50个字符').optional(),
    expiresAt: z.coerce.date().optional(),
});



/**
 * Parameter validation schemas
 */
export const UserStorageParamsSchema = z.object({
    storageId: commonSchemas.uuid,
});

export const UserStorageKeyParamsSchema = z.object({
    key: z.string().min(1, '键名不能为空'),
});

/**
 * Database entity types
 */
export type UserStorageEntity = UserStorage;
export type CreateUserStorageEntity = Omit<UserStorage, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateUserStorageEntity = Partial<Omit<UserStorage, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>; 