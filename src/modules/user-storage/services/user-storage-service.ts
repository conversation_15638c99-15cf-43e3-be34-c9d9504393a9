/**
 * User Storage Service
 * 
 * Business logic layer for user storage operations.
 * Handles user storage CRUD operations based on key.
 */

import { injectable, inject } from 'tsyringe';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { CreateUserStorageEntity, UserStorageRepository } from '../repositories/user-storage-repository';
import { UserStorageMapper } from './user-storage-mapper';
import { TYPES } from '@/shared/constants';
import type { 
    CreateOrUpdateUserStorageRequestDto,
    UserStorageResponseDto,
} from '../dto';
import { NotFoundError } from '@/shared/utils/errors';

@injectable()
export class UserStorageService {
    constructor(
        @inject(TYPES.UserStorageRepository) private readonly userStorageRepository: UserStorageRepository,
        @inject(TYPES.Logger) private readonly logger: typeof Logger,
        @inject(TYPES.UserStorageMapper) private readonly userStorageMapper: UserStorageMapper
    ) {}

    /**
     * Get user storage by key
     */
    async getByKey(key: string, userId: string): Promise<UserStorageResponseDto | null> {
        const storage = await this.userStorageRepository.findByKey(key, userId);
        
        if (!storage) {
            throw new NotFoundError('User storage not found');
        }

        this.logger.info('User storage retrieved successfully', { 
            storageId: storage.id,
            key,
            userId,
        });

        return this.userStorageMapper.toDto(storage);
    }

    /**
     * Create user storage by key
     */
    async createByKey(
        key: string, 
        userId: string, 
        data: CreateOrUpdateUserStorageRequestDto
    ): Promise<UserStorageResponseDto> {
        const storageData: CreateUserStorageEntity = {
            userId,
            key,
            value: data.value,
            description: data.description,
            version: data.version ?? '1.0',
            expiresAt: data.expiresAt ?? null,
        };

        const storage = await this.userStorageRepository.create(storageData);

        this.logger.info('User storage created successfully', {
            storageId: storage.id,
            key,
            userId,
            version: storage.version,
        });

        return this.userStorageMapper.toDto(storage);
    }

    /**
     * Update user storage by key
     */
    async updateByKey(
        key: string, 
        userId: string,
        data: CreateOrUpdateUserStorageRequestDto
    ): Promise<UserStorageResponseDto | null> {
        // First check if the storage exists
        const existing = await this.userStorageRepository.findByKey(key, userId);
        if (!existing) {
            throw new NotFoundError('User storage not found');
        }

        const storage = await this.userStorageRepository.update(key, data);

        if(!storage){
            throw new NotFoundError('User storage not found after update');
        }

        this.logger.info('User storage updated successfully', {
            storageId: storage.id,
            key,
            userId,
        });

        return this.userStorageMapper.toDto(storage);
    }

    /**
     * Delete user storage by key
     */
    async deleteByKey(key: string, userId: string): Promise<{key:string}> {
        const existing = await this.userStorageRepository.findByKey(key, userId);
        if (!existing) {
            throw new NotFoundError('User storage not found');
        }

        const deleted = await this.userStorageRepository.deleteByKey(key, userId);

        if (!deleted) {
            throw new NotFoundError('User storage not found for deletion');
        }

        this.logger.info('User storage deleted successfully', {
            key,
            userId,
        });

        return {
            key
        };
    }
} 