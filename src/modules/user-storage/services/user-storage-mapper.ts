/**
 * User Storage Mapper
 * 
 * Maps between user storage entities and DTOs.
 */

import { injectable } from 'tsyringe';
import type { UserStorageEntity } from '../repositories/user-storage-repository';
import type { UserStorageResponseDto } from '../dto/user-storage-dto';
import { getDateTime } from '@/shared/utils';
@injectable()
export class UserStorageMapper {
    /**
     * Converts a user storage entity to a response DTO.
     */
    public toDto(entity: UserStorageEntity): UserStorageResponseDto {
        const dto: UserStorageResponseDto = {
            id: entity.id,
            userId: entity.userId,
            key: entity.key,
            value: entity.value,
            version: entity.version ?? '1.0',
            createdAt: getDateTime(entity.createdAt),
            updatedAt: getDateTime(entity.updatedAt),
        };

        if (entity.description !== null) {
            dto.description = entity.description;
        }

        if (entity.expiresAt) {
            dto.expiresAt = entity.expiresAt;
        }

        return dto;
    }

    /**
     * Converts a list of user storage entities to a list of response DTOs.
     */
    public toDtoList(entities: UserStorageEntity[]): UserStorageResponseDto[] {
        return entities.map(entity => this.toDto(entity));
    }
} 