/**
 * Health Controller
 * 
 * Handles HTTP requests for health check operations.
 */

import type { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { injectable, inject } from 'tsyringe';
import { TYPES } from '@/shared/constants';
import { createSuccessResponse } from '@/shared/utils/response-formatter';
import type { HealthService } from '../services';
import { getDateTime } from '@/shared/utils/time';

@injectable()
export class HealthController {
    constructor(@inject(TYPES.HealthService) private readonly healthService: HealthService) {
    }

    /**
     * Basic health check endpoint
     * GET /health
     */
    public getBasicHealth = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const health = await this.healthService.getSimpleHealth();
            const response = createSuccessResponse(health, 'System health check');
            
            // Check if health status indicates healthy
            const isHealthy = health.status === 'healthy';
            res.status(isHealthy ? StatusCodes.OK : StatusCodes.SERVICE_UNAVAILABLE).json(response);
        } catch (error) {
            next(error);
        }
    };

    /**
     * Detailed health check endpoint
     * GET /health/detailed
     */
    public getDetailedHealth = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const detailedHealth = await this.healthService.performHealthCheck();
            const response = createSuccessResponse(detailedHealth, 'Detailed health check');
            
            // Check if detailed health status indicates healthy
            const isHealthy = detailedHealth.status === 'healthy';
            res.status(isHealthy ? StatusCodes.OK : StatusCodes.SERVICE_UNAVAILABLE).json(response);
        } catch (error) {
            next(error);
        }
    };

    /**
     * Readiness probe endpoint
     * GET /health/ready
     */
    public getReadiness = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const readiness = await this.healthService.getReadiness();
            
            if (readiness.ready) {
                res.status(StatusCodes.OK).json({ 
                    status: 'ready', 
                    checks: readiness.checks 
                });
            } else {
                res.status(StatusCodes.SERVICE_UNAVAILABLE).json({ 
                    status: 'not ready', 
                    checks: readiness.checks 
                });
            }
        } catch (error) {
            next(error);
        }
    };

    /**
     * Liveness probe endpoint
     * GET /health/live
     */
    public getLiveness = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const liveness = await this.healthService.getLiveness();
            
            res.status(StatusCodes.OK).json({ 
                status: liveness.alive ? 'alive' : 'dead',
                alive: liveness.alive 
            });
        } catch (error) {
            next(error);
        }
    };

    /**
     * 最简单的存活检查，只返回 pong
     * GET /health/live
     */
    public getPong = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
         try {
            res.status(StatusCodes.OK).json({
                message: 'pong',
                timestamp: getDateTime(),
                uptime: process.uptime(),
            });
        } catch (error) {
            next(error);
        }
    };

    public getServerStatus = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            res.status(StatusCodes.OK).json({
                message: 'server status',
                timestamp: getDateTime(),
                uptime: process.uptime(),
            });
        } catch (error) {
            next(error);
        }
    };
} 