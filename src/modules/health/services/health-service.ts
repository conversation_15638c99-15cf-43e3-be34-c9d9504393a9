/**
 * Health Service
 * 
 * Business logic layer for health check operations.
 * Handles system health monitoring and status reporting.
 */

import { injectable, inject } from 'tsyringe';
import { databaseService } from '@/infrastructure/database';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { TYPES } from '@/shared/constants';
import { getDateTime } from '@/shared/utils';
import type {
    HealthCheckDto,
    ServiceHealthDto,
    SimpleHealthDto,
    ReadinessDto,
    LivenessDto,
} from '../dto';

// Internal health check status interface
interface DetailedHealthCheckStatus {
    status: 'pass' | 'fail' | 'warn';
    responseTime?: number;
    message?: string;
    details?: Record<string, unknown>;
}

@injectable()
export class HealthService {
    private readonly startTime: number = Date.now();
    private readonly version: string = process.env['npm_package_version'] ?? '1.0.0';

    constructor(
        @inject(TYPES.Logger) private readonly logger: typeof Logger
    ) {}

    /**
     * Perform complete health check
     */
    async performHealthCheck(): Promise<HealthCheckDto> {
        const startTime = Date.now();
        this.logger.info('Starting health check');

        const [databaseCheck, memoryCheck] = await Promise.allSettled([
            this.checkDatabase(),
            this.checkMemory(),
        ]);

        const checks = {
            database:
                databaseCheck.status === 'fulfilled'
                    ? databaseCheck.value
                    : this.createFailedCheck('Database check failed'),
            memory:
                memoryCheck.status === 'fulfilled'
                    ? memoryCheck.value
                    : this.createFailedCheck('Memory check failed'),
        };

        const overallStatus = this.determineOverallStatus(checks);
        const duration = Date.now() - startTime;

        // Get memory usage for the external format
        const memUsage = process.memoryUsage();
        const memUsageMB = {
            used: Math.round(memUsage.heapUsed / 1024 / 1024),
            total: Math.round(memUsage.heapTotal / 1024 / 1024),
            percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
        };

        const result: HealthCheckDto = {
            status: overallStatus,
            timestamp: getDateTime(),
            uptime: Math.floor((Date.now() - this.startTime) / 1000),
            version: this.version,
            services: {
                database: this.convertToServiceHealth(checks.database),
            },
            memory: memUsageMB,
            cpu: {
                usage: 0, // CPU usage calculation would require additional implementation
            },
        };

        this.logger.info('Health check completed successfully', {
            status: overallStatus,
            duration,
            checks: Object.keys(checks).reduce(
                (acc, key) => {
                    acc[key] = checks[key as keyof typeof checks].status;
                    return acc;
                },
                {} as Record<string, string>,
            ),
        });

        return result;
    }

    /**
     * Get simple health status
     */
    async getSimpleHealth(): Promise<SimpleHealthDto> {
        const healthCheck = await this.performHealthCheck();
        
        this.logger.info('Simple health check retrieved successfully', {
            status: healthCheck.status,
        });

        return {
            status: healthCheck.status,
            timestamp: healthCheck.timestamp,
        };
    }

    /**
     * Get readiness status
     */
    async getReadiness(): Promise<ReadinessDto> {
        const checks: string[] = [];
        let allReady = true;

        // Database readiness check
        try {
            await databaseService.healthCheck();
            checks.push('database: ready');
        } catch {
            checks.push('database: not ready');
            allReady = false;
        }

        this.logger.info('Readiness check completed', {
            ready: allReady,
            checksCount: checks.length,
        });

        return {
            ready: allReady,
            checks,
        };
    }

    /**
     * Get liveness status
     */
    async getLiveness(): Promise<LivenessDto> {
        // Simple liveness check - if we can execute this, the process is alive
        this.logger.info('Liveness check completed successfully');

        return {
            alive: true,
        };
    }

    /**
     * Check database connection
     */
    private async checkDatabase(): Promise<DetailedHealthCheckStatus> {
        const startTime = Date.now();

        try {
            // Execute simple database query to test connection
            await databaseService.healthCheck();

            const responseTime = Date.now() - startTime;

            if (responseTime > 1000) {
                return {
                    status: 'warn',
                    responseTime,
                    message: 'Database response time is slow',
                    details: { threshold: '1000ms', actual: `${responseTime}ms` },
                };
            }

            return {
                status: 'pass',
                responseTime,
                message: 'Database connection is healthy',
            };
        } catch (error) {
            this.logger.error('Database health check failed', {}, error as Error);

            return {
                status: 'fail',
                responseTime: Date.now() - startTime,
                message: 'Database connection failed',
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }

    /**
     * Check memory usage
     */
    private async checkMemory(): Promise<DetailedHealthCheckStatus> {
        try {
            const memUsage = process.memoryUsage();
            const memUsageMB = {
                rss: Math.round(memUsage.rss / 1024 / 1024),
                heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
                heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
                external: Math.round(memUsage.external / 1024 / 1024),
            };

            // Warning thresholds
            const heapUsedThreshold = 512; // 512MB
            const rssThreshold = 1024; // 1GB

            if (memUsageMB.heapUsed > heapUsedThreshold || memUsageMB.rss > rssThreshold) {
                return {
                    status: 'warn',
                    message: 'High memory usage detected',
                    details: {
                        memory: memUsageMB,
                        thresholds: {
                            heapUsed: `${heapUsedThreshold}MB`,
                            rss: `${rssThreshold}MB`,
                        },
                    },
                };
            }

            return {
                status: 'pass',
                message: 'Memory usage is normal',
                details: { memory: memUsageMB },
            };
        } catch (error) {
            this.logger.error('Memory check failed', {}, error as Error);

            return {
                status: 'fail',
                message: 'Memory check failed',
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }

    /**
     * Create failed check result
     */
    private createFailedCheck(message: string): DetailedHealthCheckStatus {
        return {
            status: 'fail',
            message,
        };
    }

    /**
     * Determine overall status from individual checks
     */
    private determineOverallStatus(
        checks: Record<string, DetailedHealthCheckStatus>,
    ): 'healthy' | 'unhealthy' | 'degraded' {
        const statuses = Object.values(checks).map((check) => check.status);

        if (statuses.every((status) => status === 'pass')) {
            return 'healthy';
        }

        if (statuses.some((status) => status === 'fail')) {
            return 'unhealthy';
        }

        // If we have warnings but no failures
        return 'degraded';
    }

    /**
     * Convert to service health format
     */
    private convertToServiceHealth(check: DetailedHealthCheckStatus): ServiceHealthDto {
        const baseResult: ServiceHealthDto = {
            status: check.status === 'pass' ? 'healthy' : check.status === 'warn' ? 'degraded' : 'unhealthy',
        };

        if (check.responseTime !== undefined) {
            baseResult.responseTime = check.responseTime;
        }

        if (check.message !== undefined) {
            baseResult.message = check.message;
        }

        if (check.details) {
            baseResult.details = check.details;
        }

        return baseResult;
    }
} 