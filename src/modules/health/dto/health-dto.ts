/**
 * Health Check DTOs
 * 
 * Data Transfer Objects for health check operations.
 */

export interface HealthCheckDto {
    status: 'healthy' | 'unhealthy' | 'degraded';
    timestamp: string;
    uptime: number;
    version: string;
    services: {
        database: ServiceHealthDto;
        [key: string]: ServiceHealthDto;
    };
    memory: MemoryUsageDto;
    cpu: CpuUsageDto;
}

export interface ServiceHealthDto {
    status: 'healthy' | 'unhealthy' | 'degraded';
    responseTime?: number;
    message?: string;
    error?: string;
    details?: Record<string, unknown>;
}

export interface MemoryUsageDto {
    used: number;
    total: number;
    percentage: number;
}

export interface CpuUsageDto {
    usage: number;
}

export interface SimpleHealthDto {
    status: string;
    timestamp: string;
}

export interface ReadinessDto {
    ready: boolean;
    checks: string[];
}

export interface LivenessDto {
    alive: boolean;
}

export interface HealthStatusDto {
    status: string;
    ready?: boolean;
    alive?: boolean;
    checks?: string[];
    error?: string;
} 