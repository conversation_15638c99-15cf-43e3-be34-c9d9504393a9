/**
 * Session Repository
 * 
 * Data access layer for session-related database operations.
 */

import { injectable } from 'tsyringe';
import { desc, eq, and, count } from 'drizzle-orm';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { session, type SessionInsert } from '@/infrastructure/database/schema/session';
import { agent } from '@/infrastructure/database/schema/agent';
import { message } from '@/infrastructure/database/schema/message';
import { databaseService } from '@/infrastructure/database';
import { logDatabaseOperation } from '@/infrastructure/logger';
import { measureTime } from '@/shared/utils/time';
import { generateUUID } from '@/shared/utils/crypto';
import type { SessionListQueryDto } from '../dto';

export interface SessionEntity {
    id: string;
    title: string;
    userId: string;
    agentId: string;
    metadata: Record<string, unknown> | null;
    createdAt: Date;
    updatedAt: Date;
}
export interface CreateSessionEntity extends Omit<SessionInsert, 'id' | 'createdAt' | 'updatedAt'> {}
export interface UpdateSessionEntity extends Partial<Pick<SessionInsert, 'title' >> {}

export interface SessionListResult {
    sessions: SessionEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}

@injectable()
export class SessionRepository {
    private get db(): PostgresJsDatabase<Record<string, unknown>> {
        return databaseService.getDb();
    }

    /**
     * Find session by ID
     */
    async findById(id: string): Promise<SessionEntity | null> {
        const { result: sessions, duration } = await measureTime(async () => {
            return await this.db
                .select()
                .from(session)
                .where(eq(session.id, id))
                .limit(1);
        });

        logDatabaseOperation('SELECT', 'session', duration, {
            sessionId: id,
            found: sessions.length > 0,
        });

        return sessions[0] ?? null;
    }

    /**
     * Create new session
     */
    async create(data: CreateSessionEntity): Promise<SessionEntity> {
        const sessionData: SessionInsert = {
            ...data,
            id: generateUUID(),
            createdAt: new Date(),
            updatedAt: new Date(),
        };

        const { result: createdSessions, duration } = await measureTime(async () => {
            return await this.db.insert(session).values(sessionData).returning();
        });

        const createdSession = createdSessions[0];
        if (!createdSession) {
            throw new Error('Failed to create session - no data returned');
        }

        logDatabaseOperation('INSERT', 'session', duration, {
            sessionId: createdSession.id,
            userId: data.userId,
            agentId: data.agentId,
        });

        return createdSession;
    }

    /**
     * Update session
     */
    async update(id: string, data: UpdateSessionEntity): Promise<SessionEntity | null> {
        const updateData = {
            ...data,
            updatedAt: new Date(),
        };

        const { result: updatedSessions, duration } = await measureTime(async () => {
            return await this.db
                .update(session)
                .set(updateData)
                .where(eq(session.id, id))
                .returning();
        });

        logDatabaseOperation('UPDATE', 'session', duration, {
            sessionId: id,
            updated: updatedSessions.length > 0,
        });

        return updatedSessions[0] ?? null;
    }

    /**
     * Delete session
     */
    async delete(id: string): Promise<boolean> {
        const { result: deletedSessions, duration } = await measureTime(async () => {
            return await this.db
                .delete(session)
                .where(eq(session.id, id))
                .returning();
        });

        logDatabaseOperation('DELETE', 'session', duration, {
            sessionId: id,
            deleted: deletedSessions.length > 0,
        });

        return deletedSessions.length > 0;
    }

    /**
     * Find sessions by user with pagination and filtering
     */
    async findByUser(userId: string, query: SessionListQueryDto): Promise<SessionListResult> {
        const offset = (query.page - 1) * query.limit;

        // Build where conditions
        const conditions = [eq(session.userId, userId)];
        if (query.agentGroup !== undefined) {
            conditions.push(eq(agent.group, query.agentGroup));
        }

        const queryBuilder = this.db
            .select({ count: count() })
            .from(session)
            .leftJoin(agent, eq(session.agentId, agent.id))
            .where(and(...conditions));

        // Get total count
        const { result: totalResult, duration: countDuration } = await measureTime(async () => {
            return await queryBuilder;
        });

        const total = totalResult[0]?.count ?? 0;

        // Get sessions with pagination
        const { result: sessions, duration: selectDuration } = await measureTime(async () => {
            return await this.db
                .select()
                .from(session)
                .leftJoin(agent, eq(session.agentId, agent.id))
                .where(and(...conditions))
                .orderBy(desc(session.updatedAt))
                .limit(query.limit)
                .offset(offset);
        });

        const sessionEntities = sessions.map(item => item.session);

        logDatabaseOperation('SELECT', 'session', countDuration + selectDuration, {
            userId,
            total,
            page: query.page,
            limit: query.limit,
            agentGroup: query.agentGroup,
        });

        const totalPages = Math.ceil(total / query.limit);
        const hasNext = query.page < totalPages;
        const hasPrev = query.page > 1;

        return {
            sessions: sessionEntities,
            total,
            page: query.page,
            limit: query.limit,
            totalPages,
            hasNext,
            hasPrev,
        };
    }


    /**
     * Find latest sessions by agent and user
     */
    async findLatestByAgentAndUser(agentId: string, userId: string, limit: number = 2): Promise<SessionEntity[]> {
        const { result: sessions, duration } = await measureTime(async () => {
            return await this.db
                .select()
                .from(session)
                .where(and(
                    eq(session.agentId, agentId),
                    eq(session.userId, userId)
                ))
                .orderBy(desc(session.updatedAt))
                .limit(limit);
        });

        logDatabaseOperation('SELECT', 'session', duration, {
            agentId,
            userId,
            limit,
            found: sessions.length,
            operation: 'findLatestByAgentAndUser',
        });

        return sessions;
    }

    /**
     * Delete all messages for a session
     */
    async deleteSessionMessages(sessionId: string): Promise<number> {
        const { result: deletedMessages, duration } = await measureTime(async () => {
            return await this.db
                .delete(message)
                .where(eq(message.sessionId, sessionId))
                .returning();
        });

        logDatabaseOperation('DELETE', 'message', duration, {
            sessionId,
            deletedCount: deletedMessages.length,
        });

        return deletedMessages.length;
    }
} 