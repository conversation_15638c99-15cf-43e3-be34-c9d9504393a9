/**
 * Chat Controller
 *
 * Handles HTTP requests for chat operations.
 */
import { injectable, inject } from 'tsyringe';
import { createSession } from 'better-sse';
import { BaseController } from '@/shared/utils/base-controller';
import { ChatService } from '../services';
import { TYPES } from '@/shared/constants';
import type { ChatRequestDto } from '../dto/session-dto';
import type { AuthenticatedRequest } from '@/shared/types';
import type { Response } from 'express';

@injectable()
export class ChatController extends BaseController {
    constructor(
        @inject(TYPES.ChatService) private readonly chatService: ChatService
    ) {
        super();
    }

    /**
     * chat - 使用 better-sse 处理聊天流
     */
    public chat = this.asyncHandler(
        async (req: AuthenticatedRequest, res: Response): Promise<void> => {
            const { sessionId } = req.validatedParams as { sessionId: string };
            const chatRequestDto = req.validatedBody as ChatRequestDto;
            const userId = this.getUserId(req);

            // 使用 better-sse 创建 SSE 会话
            const sseSession = await createSession(req, res);

            // 使用 ChatService 处理聊天逻辑
            // 这里不需要 await，因为 ChatService 会异步处理并通过 session 推送事件
            void this.chatService.chat(sseSession, sessionId, userId, chatRequestDto, res);

            // better-sse 会自动处理连接关闭和清理
        }
    );
}