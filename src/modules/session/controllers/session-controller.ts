/**
 * Session Controller
 *
 * Handles HTTP requests for session operations.
 */
import { injectable, inject } from 'tsyringe';
import { BaseController } from '@/shared/utils/base-controller';
import { SessionService } from '../services';
import { TYPES } from '@/shared/constants';
import { StatusCodes } from 'http-status-codes';
import type {
    CreateSessionRequestDto,
    UpdateSessionRequestDto,
    SessionListQueryDto,
    SessionResponseDto,
    SessionWithAgentResponseDto,
} from '../dto/session-dto';
import type { AuthenticatedRequest, PaginationMetadata, TypedResponse } from '@/shared/types';

@injectable()
export class SessionController extends BaseController {
    constructor(
        @inject(TYPES.SessionService) private readonly sessionService: SessionService
    ) {
        super();
    }

    /**
     * Get user sessions
     */
    public getUserSessions = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<SessionWithAgentResponseDto[]>): Promise<void> => {
            const query = req.validatedQuery as SessionListQueryDto;
            const userId = this.getUserId(req);
            const result = await this.sessionService.getUserSessions(userId, query);
            const pagination: PaginationMetadata = {
                page: result.page,
                limit: result.limit,
                hasNext: result.hasNext,
                hasPrev: result.hasPrev,
                total: result.total,
                totalPages: result.totalPages,
            }
            this.sendPaginatedResponse(res, result.sessions, pagination, 'Sessions retrieved successfully');
        }
    );

    /**
     * Get a session by ID
     */
    public getSessionById = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<SessionResponseDto>): Promise<void> => {
            const { sessionId } = req.validatedParams as { sessionId: string };
            const userId = this.getUserId(req);
            const session = await this.sessionService.getSessionById(sessionId, userId);
            this.sendSuccess(res, session, 'Session retrieved successfully');
        }
    );

    /**
     * Create a new session
     */
    public createSession = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<SessionResponseDto>): Promise<void> => {
            const createSessionDto = req.validatedQuery as CreateSessionRequestDto;
            const userId = this.getUserId(req);
            const session = await this.sessionService.createSession(userId, createSessionDto);
            this.sendSuccess(res, session, 'Session created successfully', StatusCodes.CREATED);
        }
    );

    /**
     * Update a session
     */
    public updateSession = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<SessionResponseDto>): Promise<void> => {
            const { sessionId } = req.validatedParams as { sessionId: string };
            const updateSessionDto = req.validatedBody as UpdateSessionRequestDto;
            const userId = this.getUserId(req);
            const updatedSession = await this.sessionService.updateSession(sessionId, userId, updateSessionDto);
            this.sendSuccess(res, updatedSession, 'Session updated successfully');
        }
    );

    /**
     * Delete a session
     */
    public deleteSession = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<{ id: string }>): Promise<void> => {
            const { sessionId } = req.validatedParams as { sessionId: string };
            const userId = this.getUserId(req);
            const result = await this.sessionService.deleteSession(sessionId, userId);
            this.sendSuccess(res, result, 'Session deleted successfully');
        }
    );
}