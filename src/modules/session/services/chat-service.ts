/**
 * Chat Service
 *
 * Handles chat operations by communicating with external agents via A2A protocol.
 */
import { injectable, inject } from 'tsyringe';
import {
    MessageSendParams,
    TaskArtifactUpdateEvent,
    TaskStatusUpdateEvent,
    Message as A2AMessage,
    Artifact as A2AArtifact
} from '@a2a-js/sdk';
import { XuiA2AClient } from '@/shared/utils/xui-a2a-client';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { TYPES } from '@/shared/constants';
import { AgentService } from '@/modules/agent/services';
import type { ChatRequestDto } from '@/modules/session/dto';
import { SessionService } from './session-service';
import { randomUUID } from 'crypto';
import {
    convertA2AMessageToA2U,
    convertA2UMessageToA2A,
    createMessageEndEvent,
    createMessageStartEvent,
    createPartContentEvent,
    createPartEndEvent,
    createPartStartEvent,
    createSessionErrorEvent,
    createSessionFinishEvent,
    createSessionStartEvent,
    eventDelta
} from '@/shared/utils/message-converter';
import { A2UUserMessage } from '@xui-web-app/a2u';
import type { Session as SSESession } from 'better-sse';
import type { Request, Response } from 'express';
import { MessageService } from '@/modules/message/services/message-service';
import { CreateMessageRequestDto, UpdateMessageRequestDto } from '@/modules/message/dto/message-dto';
import type { MessageContent as DBA2UMessageContent } from '@/infrastructure/database/schema/message';

@injectable()
export class ChatService {
    private messageId: string = '';
    private isPart: boolean = false;
    private partIndex: number = -1;
    private partType: string = 'text';

    private dbId: string = '';
    private agentMessage: CreateMessageRequestDto | undefined = undefined;
    private a2uMessageContent: DBA2UMessageContent | undefined = undefined;
    private readonly tags: Set<string> = new Set();

    constructor(
        @inject(TYPES.Logger) private readonly logger: typeof Logger,
        @inject(TYPES.AgentService) private readonly agentService: AgentService,
        @inject(TYPES.SessionService) private readonly sessionService: SessionService,
        @inject(TYPES.MessageService) private readonly messageService: MessageService
    ) { }

    public async chat(
        sseSession: SSESession,
        sessionId: string,
        userId: string,
        chatRequestDto: ChatRequestDto,
        res: Response
    ): Promise<void> {

        this.dbId = '';
        this.tags.clear();
        this.messageId = '';
        this.partIndex = -1;
        this.isPart = false;

        this.a2uMessageContent = undefined;
        this.agentMessage = {
            id: randomUUID(),
            sessionId,
            role: 'assistant',
            content: [],
            sender: {
                id: chatRequestDto.agentId,
            },
        };

        // 保存消息到数据库
        await this.saveMessageToDatabase(sessionId, userId, chatRequestDto);

        // 发送会话开始事件
        this.sendSessionStartEvent(sseSession, sessionId);


        try {
            // 验证代理和会话
            const agentServerUrl = await this.validateAgentAndSession(
                sseSession,
                sessionId,
                userId,
                chatRequestDto.agentId
            );

            // 验证消息是否为空，如果为空，则验证当前会话是否存在消息，如果存在则报错
            // 暂时不验证，因为由前端来判断需不需要发启动消息
            // if (!chatRequestDto.message) {
            //     const hasMessages = await this.messageService.hasMessagesBySessionId(sessionId);
            //     if (hasMessages) {
            //         throw new Error('Message is empty, but session has messages.');
            //     }
            // }

            // 设置并执行 A2A 通信
            await this.executeA2ACommunication(
                sseSession,
                sessionId,
                userId,
                chatRequestDto,
                agentServerUrl
            );

            // 发送会话完成事件
            this.sendSessionFinishEvent(sseSession, sessionId, res);

            await this.updateAgentMessageToDatabase(this.dbId, userId);

        } catch (error) {
            // 处理错误
            this.handleChatError(error, sseSession, sessionId, userId, chatRequestDto.agentId, res);
            await this.messageService.deleteMessage(this.dbId, userId);
        }
    }

    /**
     * 发送会话开始事件
     */
    private sendSessionStartEvent(sseSession: SSESession, sessionId: string): void {
        const sessionStartEvent = createSessionStartEvent(sessionId);
        sseSession.push(sessionStartEvent, 'session-start');
    }

    /**
     * 发送会话完成事件并关闭连接
     */
    private sendSessionFinishEvent(sseSession: SSESession, sessionId: string, res: Response): void {
        const sessionFinishEvent = createSessionFinishEvent(sessionId);
        try {
            sseSession.push(sessionFinishEvent, 'session-finish');
        } catch (e) {
            this.logger.warn(
                'Could not push session-finish event, client may have disconnected.',
                { error: (e as Error).message, sessionId }
            );
        }

        if (!res.writableEnded) {
            res.end();
            this.logger.info('SSE connection explicitly ended.', { sessionId });
        }
    }

    /**
     * 验证代理和会话的有效性
     * 
     * @param sseSession SSE 会话对象
     * @param sessionId 会话 ID
     * @param userId 用户 ID
     * @param agentId 代理 ID
     * @returns 代理服务器 URL
     */
    private async validateAgentAndSession(
        sseSession: SSESession,
        sessionId: string,
        userId: string,
        agentId: string
    ): Promise<string> {
        // 验证代理
        const agent = await this.agentService.getAgentById(agentId, userId);
        const cardUrl = agent.cardUrl;

        if (!cardUrl) {
            const sessionErrorEvent = createSessionErrorEvent(sessionId, 'AGENT_CARD_URL_NOT_FOUND');
            sseSession.push(sessionErrorEvent, 'session-error');
            throw new Error(`Agent with ID ${agentId} does not have a configured card URL.`);
        }

        // 验证会话 - getSessionById 如果未找到会话会抛出异常
        await this.sessionService.getSessionById(sessionId, userId);

        return cardUrl;
    }

    /**
     * 执行 A2A 通信
     * 
     * @param sseSession SSE 会话对象
     * @param sessionId 会话 ID
     * @param userId 用户 ID
     * @param chatRequestDto 聊天请求 DTO
     * @param agentServerUrl 代理服务器 URL
     */
    private async executeA2ACommunication(
        sseSession: SSESession,
        sessionId: string,
        userId: string,
        chatRequestDto: ChatRequestDto,
        agentServerUrl: string
    ): Promise<void> {
        // 准备 A2A 参数
        const a2aParams = this.getA2AParamsFromMessage(chatRequestDto, userId);
        this.messageId = a2aParams.message.messageId;

        // 临时解决方案，即消息的taskId和contextId都用sessionId
        a2aParams.message.taskId = sessionId;
        a2aParams.message.contextId = sessionId;

        // 获取客户端请求头，用于转发到 A2A 代理
        const request = sseSession.getRequest() as unknown as Request;
        const headers = request.headers;
        
        
        // 使用 SafeA2AClient 确保请求头能正确传递给 A2A 代理，
        const client = new XuiA2AClient(agentServerUrl);
        client.setHeader('userId', userId);
        client.setHeader('token', headers['token'] as string);
        const agentStream = client.sendMessageStream(a2aParams);

        // 处理 A2A 流
        await this.processA2AStream(agentStream, sseSession, sessionId, userId, chatRequestDto.agentId);
    }

    /**
     * 处理聊天过程中的错误
     * 
     * @param error 错误对象
     * @param sseSession SSE 会话对象
     * @param sessionId 会话 ID
     * @param userId 用户 ID
     * @param agentId 代理 ID
     */
    private handleChatError(
        error: unknown,
        sseSession: SSESession,
        sessionId: string,
        userId: string,
        agentId: string,
        res: Response
    ): void {
        this.logger.error('Failed to initiate chat with agent.', {
            error: (error as Error).message,
            agentId,
            sessionId,
            userId,
        });

        // 发送错误事件
        const sessionErrorEvent = createSessionErrorEvent(sessionId, (error as Error).message);
        sseSession.push(sessionErrorEvent, 'session-error');

        this.sendSessionFinishEvent(sseSession, sessionId, res);

    }

    /**
     * 处理 A2A 流数据并通过 SSE 推送给客户端
     * 
     * @param agentStream A2A 客户端返回的异步生成器
     * @param sseSession better-sse 会话对象
     * @param sessionId 会话 ID
     * @param userId 用户 ID
     * @param agentId 代理 ID
     */
    private async processA2AStream(
        agentStream: AsyncGenerator<unknown, void, unknown>,
        sseSession: SSESession,
        sessionId: string,
        userId: string,
        agentId: string
    ): Promise<void> {
        const messageStartEvent = createMessageStartEvent(this.messageId, {
            id: agentId,
        });
        sseSession.push(messageStartEvent, 'message-start');

        for await (const event of agentStream) {
            try {
                const a2aEvent = event as unknown as { kind: string;[key: string]: unknown };
                this.processA2AStreamEvent(a2aEvent, sseSession, sessionId, userId, agentId);
            } catch (writeError) {
                this.logger.error('Error pushing chunk to SSE session', {
                    error: writeError,
                    sessionId,
                    userId,
                    agentId
                });
                break;
            }
        }
    }

    private processA2AStreamEvent(
        a2aEvent: unknown,
        sseSession: SSESession,
        sessionId: string,
        userId: string,
        agentId: string
    ): void {
        const event = a2aEvent as { kind: string; [key: string]: unknown };
        
        if (event.kind === 'task') {
            // 暂不做处理
            return;
        }
        
        if (event.kind === 'status-update') {
            const statusEvent = a2aEvent as unknown as TaskStatusUpdateEvent;
            if (statusEvent.final) {
                const messageEndEvent = createMessageEndEvent(this.messageId);
                sseSession.push(messageEndEvent, 'message-end');
            } else if (statusEvent.status.message) {
                this.processPartData(statusEvent.status.message, sseSession, sessionId, userId, agentId);
            }
        } else if (event.kind === 'artifact-update') {
            const artifactEvent = a2aEvent as unknown as TaskArtifactUpdateEvent;
            const firstPart = artifactEvent.artifact.parts[0];
            if (
                firstPart?.kind === 'data' &&
                'type' in firstPart.data &&
                firstPart.data['type'] === 'PART_START'
            ) {
                this.isPart = true;
                this.partIndex++;

                const partType = firstPart.data['partType'] as string;
                const contentType = partType.toLowerCase() as 'text' | 'file' | 'data';
                this.partType = contentType;

                this.tags.add(contentType)

                const partStartEvent = createPartStartEvent(this.partIndex, contentType);
                sseSession.push(partStartEvent, 'part-start');

                // 根据内容类型创建正确的消息内容对象
                switch (contentType) {
                    case 'text':
                        this.a2uMessageContent = { type: 'text', text: '' };
                        break;
                    case 'file':
                        this.a2uMessageContent = { type: 'file' } as DBA2UMessageContent;
                        break;
                    case 'data':
                        this.a2uMessageContent = { type: 'data' } as DBA2UMessageContent;
                        break;
                }

            } else if (
                firstPart?.kind === 'data' &&
                'type' in firstPart.data &&
                firstPart.data['type'] === "PART_END"
            ) {
                const partEndEvent = createPartEndEvent(this.partIndex);
                sseSession.push(partEndEvent, 'part-end');
                this.isPart = false;

                if (this.a2uMessageContent) {
                    this.agentMessage?.content.push(this.a2uMessageContent);
                    this.a2uMessageContent = undefined;
                }
            } else {
                this.processPartData(artifactEvent.artifact, sseSession, sessionId, userId, agentId);
            }
        } else {
            const messageEvent = a2aEvent as unknown as A2AMessage;
            this.processPartData(messageEvent, sseSession, sessionId, userId, agentId);
        }
    }

    private processPartData(
        message: A2AMessage | A2AArtifact,
        sseSession: SSESession,
        _sessionId: string,
        _userId: string,
        agentId: string
    ): void {
        if (this.isPart === true) {
            const a2uMessage = convertA2AMessageToA2U(message, agentId);

            // 根据content数组第一条内容的type动态设置参数
            const firstContent = a2uMessage.content[0];
            let partContentEvent;

            if (firstContent?.type === 'text') {
                let delta: eventDelta;

                if(this.partType === 'text') {
                    delta = {
                        type: 'text_delta',
                        text: firstContent.text,
                    }
                } else {
                    delta = {
                        type: 'data_delta',
                        data: firstContent.text,
                    }
                }
                partContentEvent = createPartContentEvent(this.partIndex, a2uMessage, delta);

                this.updateMessageContent('text', firstContent.text || '');
            } else if (firstContent?.type === 'file') {
                partContentEvent = createPartContentEvent(this.partIndex, a2uMessage,
                    { type: 'file_delta' as const, file: firstContent.file }
                );
                this.updateMessageContent('file', firstContent.file);
            } else if (firstContent?.type === 'data') {
                partContentEvent = createPartContentEvent(this.partIndex, a2uMessage,
                    { type: 'data_delta' as const, data: JSON.stringify(firstContent.data) }
                );

                this.updateMessageContent('data', JSON.stringify(firstContent.data));
            } else {
                // 默认情况，保持原有逻辑
                partContentEvent = createPartContentEvent(
                    this.partIndex,
                    a2uMessage,
                    { type: 'data_delta' as const, data: JSON.stringify(a2uMessage.content) }
                );
                this.updateMessageContent('data', JSON.stringify(a2uMessage.content));
            }
            sseSession.push(partContentEvent, 'part-content');
        }
    }

    /**
     * getA2AParamsFromMessage
     * 
     * @param chatRequestDto
     * @param userId
     * @returns
     */
    private getA2AParamsFromMessage(chatRequestDto: ChatRequestDto, userId: string): MessageSendParams {
        const { message } = chatRequestDto;
        let userMessage: A2UUserMessage;

        if (message) {
            userMessage = message as A2UUserMessage;
        } else {
            // 创建启动消息对象
            userMessage = {
                id: randomUUID(),
                role: 'user' as const,
                content: [
                    {
                        type: 'text' as const,
                        text: "{\"type\":\"launch\"}",
                    },
                ],
                sender: {
                    id: userId,
                },
            };
        }

        return convertA2UMessageToA2A(userMessage as never);
    }

    private async saveMessageToDatabase(
        sessionId: string,
        userId: string,
        chatRequestDto: ChatRequestDto
    ): Promise<void> {
        const { message } = chatRequestDto;
        if (message) {
            const messageWithSessionId = {
                ...message,
                sessionId,
                userId,
            };
            await this.messageService.createMessage(messageWithSessionId as CreateMessageRequestDto);
        }

        const createdMessage = await this.messageService.createMessage(
            {...this.agentMessage, userId} as CreateMessageRequestDto
        );
        this.dbId = createdMessage.dbId;
    }

    private async updateAgentMessageToDatabase(dbId: string, userId: string): Promise<void> {
        if(this.agentMessage && this.tags.size > 0) {
            this.agentMessage.tags = Array.from(this.tags);
        }

        await this.messageService.updateMessage({dbId,...this.agentMessage, userId} as UpdateMessageRequestDto);
    }

    private updateMessageContent(type: string, value: unknown): void {
        if (!this.a2uMessageContent) {
            return;
        }

        // 根据 a2UMessageContent 的实际类型来处理内容更新
        if (this.a2uMessageContent.type === 'text') {
            // text 类型：所有内容都作为文本拼接
            const currentText = this.a2uMessageContent.text ?? '';
            this.a2uMessageContent.text = currentText + String(value);
        } else if (this.a2uMessageContent.type === 'file') {
            // file 类型：根据 contentType 处理
            if (value !== null && typeof value === 'object') {
                this.a2uMessageContent.file = value as {
                    bytes?: string;
                    uri?: string;
                    metadata?: Record<string, unknown>
                };
            }
        } else {
            if (type === 'data') {
                this.a2uMessageContent.data = value as Record<string, unknown> | string;
            } else {
                // 对于非data类型的更新，将值作为字符串添加到数组
                const currentData = this.a2uMessageContent.data ?? '';
                this.a2uMessageContent.data = currentData + String(value);
            }
        }
    }
} 