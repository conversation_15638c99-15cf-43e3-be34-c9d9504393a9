/**
 * Message Mapper
 * 
 * Maps between message-related data structures (e.g., DTOs, entities).
 */

import { injectable } from 'tsyringe';
import type { MessageEntity } from '../repositories/message-repository';
import type { MessageResponseDto, MessageRole } from '../dto/message-dto';
import { getDateTime } from '@/shared/utils';

@injectable()
export class MessageMapper {
    /**
     * Converts a message entity to a response DTO.
     */
    public toDto(message: MessageEntity): MessageResponseDto {
        const dto: MessageResponseDto = {
            dbId: message.dbId,
            id: message.id,
            sessionId: message.sessionId,
            role: message.role as MessageRole,
            content: message.content,
            extendedData: message.extendedData,
            createdAt: getDateTime(message.createdAt),
            updatedAt: getDateTime(message.updatedAt),
            sender: message.sender,
            userId: message.userId
        };
        return dto;
    }

    /**
     * Converts a list of message entities to a list of response DTOs.
     */
    public toDtoList(messages: MessageEntity[]): MessageResponseDto[] {
        return messages.map(message => this.toDto(message));
    }
} 