/**
 * Message Controller
 * 
 * Handles HTTP requests for message operations.
 * Provides endpoints for CRUD operations and chat functionality.
 */

import { injectable, inject } from 'tsyringe';
import { BaseController } from '@/shared/utils/base-controller';
import { MessageService } from '../services/message-service';
import { TYPES } from '@/shared/constants';
import type {
    MessageListQueryDto,
    MessageResponseDto,
} from '../dto/message-dto';
import type { AuthenticatedRequest, TypedResponse } from '@/shared/types';

@injectable()
export class MessageController extends BaseController {
    constructor(@inject(TYPES.MessageService) private readonly messageService: MessageService) {
        super();
    }

    /**
     * Get a message by ID
     */
    public getMessageById = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<MessageResponseDto>): Promise<void> => {
            const { dbId } = req.validatedParams as { dbId: string };
            const userId = this.getUserId(req);
            const message = await this.messageService.getMessageById(dbId, userId);
            this.sendSuccess(res, message, 'Message retrieved successfully');
        }
    );

    /**
     * Get messages for a session
     */
    public getSessionMessages = this.asyncHandler(
        async (
            req: AuthenticatedRequest,
            res: TypedResponse<{
                messages: MessageResponseDto[];
                pagination: {
                    limit: number;
                    hasNextPage: boolean;
                    nextCursor: string;
                };
            }>
        ): Promise<void> => {
            const { sessionId } = req.validatedParams as { sessionId: string };
            const query = req.validatedQuery as MessageListQueryDto;
            const userId = this.getUserId(req);
            const result = await this.messageService.getSessionMessages(userId, sessionId, query);

            const response = {
                messages: result.messages,
                pagination: {
                    limit: result.limit ?? 10,
                    hasNextPage: result.hasNextPage,
                    nextCursor: result.nextCursor ?? '',
                },
            };
            this.sendSuccess(res, response, 'Messages retrieved successfully');
        }
    );


    /**
     * Delete a message
     */
    public deleteMessage = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<{ dbId: string }>): Promise<void> => {
            const { dbId } = req.validatedParams as { dbId: string };
            const userId = this.getUserId(req);
            await this.messageService.deleteMessage(dbId, userId);
            this.sendSuccess(res, { dbId }, 'Message deleted successfully');
        }
    );
} 