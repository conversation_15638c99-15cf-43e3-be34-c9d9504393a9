/**
 * Message Validators
 * 
 * Express middleware for validating message-related requests.
 * Provides reusable validation functions for message operations.
 */

import { commonSchemas } from '@/shared/utils/validator-helpers';
import {
    createParamsValidator,
    createBodyValidator,
    createQueryValidator
} from '@/shared/utils/validator-factory';
import {
    CreateMessageRequestDtoSchema,
    MessageListQueryDtoSchema,
} from '../dto/message-dto';
import { z } from 'zod/v4';
import type { RequestHandler } from 'express';

/**
 * Pre-built validators using the generic factory functions
 */
export const validateMessageDbId: RequestHandler = createParamsValidator(
    z.object({ dbId: commonSchemas.uuid }),
    'Invalid message dbId'
);
export const validateSessionIdParam: RequestHandler = createParamsValidator(
    z.object({ sessionId: commonSchemas.uuid }),
    'Invalid session ID'
);

export const validateCreateMessageRequest: RequestHandler = createBodyValidator(
    CreateMessageRequestDtoSchema as unknown as z.ZodType, 'create message request'
);
export const validateMessageListQuery: RequestHandler = createQueryValidator(
    MessageListQueryDtoSchema as unknown as z.ZodType, 'message list query'
);

/**
 * Combined validator arrays for different operations
 */
export const validateCreateMessage: RequestHandler[] = [
    validateCreateMessageRequest,
];

export const validateListMessages: RequestHandler[] = [
    validateMessageListQuery,
];

export const validateGetMessage: RequestHandler[] = [
    validateMessageDbId,
];

export const validateDeleteMessage: RequestHandler[] = [
    validateMessageDbId,
];

/**
 * Combined validator for session messages
 */
export const validateSessionMessages: RequestHandler[] = [
    validateSessionIdParam,
    validateMessageListQuery,
];


/**
 * Combined validator for message operations
 */
export const validateMessageOperation: RequestHandler[] = [
    validateMessageDbId,
];