/**
 * Message Data Transfer Objects
 * 
 * DTOs for message-related API operations with proper validation and typing.
 */

import { z } from 'zod/v4';

// Message content types (based on actual schema)
export const MessageContentTypeSchema = z.enum([
    'text',
    'file',
    'data'
]);

export type MessageContentType = z.infer<typeof MessageContentTypeSchema>;

// Message roles (based on actual schema)
export const MessageRoleSchema = z.enum(['user', 'assistant']);
export type MessageRole = z.infer<typeof MessageRoleSchema>;

// Base Message DTO
export const MessageDtoSchema = z.object({
    dbId: z.uuid(),
    id: z.string(),
    sessionId: z.uuid(),
    role: MessageRoleSchema,
    content: z.array(z.object({
        type: MessageContentTypeSchema,
        text: z.string().optional(),
        file: z.object({
            bytes: z.string().optional(),
            uri: z.string().optional(),
            metadata: z.record(z.string(), z.unknown()).optional(),
        }).optional(),
        data: z.union([z.record(z.string(), z.unknown()), z.string()]).optional(),
        metadata: z.record(z.string(), z.unknown()).optional(),
    })).min(1),
    sender: z.object({
        id: z.string(),
        name: z.string().optional(),
        avatar: z.string().optional(),
        type: z.string().optional(),
    }).nullable(),
    extendedData: z.record(z.string(), z.unknown()).nullable(),
    createdAt: z.date(),
    updatedAt: z.date(),
    userId: z.string().nullable(),
});

export type MessageDto = z.infer<typeof MessageDtoSchema>;

// Create Message Request DTO
export const CreateMessageRequestDtoSchema = z.object({
    id: z.string(),
    sessionId: z.uuid(),
    role: MessageRoleSchema,
    content: z.array(z.object({
        type: MessageContentTypeSchema,
        text: z.string().optional(),
        file: z.object({
            bytes: z.string().optional(),
            uri: z.string().optional(),
            metadata: z.record(z.string(), z.unknown()).optional(),
        }).optional(),
        data: z.union([z.record(z.string(), z.unknown()), z.string()]).optional(),
        metadata: z.record(z.string(), z.unknown()).optional(),
    })).min(1),
    sender: z.object({
        id: z.string(),
        name: z.string().optional(),
        avatar: z.string().optional(),
        type: z.string().optional(),
    }).nullable(),
    extendedData: z.record(z.string(), z.unknown()).optional(),
    tags: z.array(z.string()).optional(),
});

export type CreateMessageRequestDto = z.infer<typeof CreateMessageRequestDtoSchema>;

export const UpdateMessageRequestDtoSchema = z.object({
    dbId: z.uuid(),
    id: z.string().optional(),
    sessionId: z.uuid().optional(),
    role: MessageRoleSchema.optional(),
    content: z.array(z.object({
        type: MessageContentTypeSchema,
        text: z.string().optional(),
        file: z.object({
            bytes: z.string().optional(),
            uri: z.string().optional(),
            metadata: z.record(z.string(), z.unknown()).optional(),
        }).optional(),
        data: z.union([z.record(z.string(), z.unknown()), z.string()]).optional(),
        metadata: z.record(z.string(), z.unknown()).optional(),
    })).min(1),
    sender: z.object({
        id: z.string(),
        name: z.string().optional(),
        avatar: z.string().optional(),
        type: z.string().optional(),
    }).nullable().optional(),
    extendedData: z.record(z.string(), z.unknown()).optional(),
    tags: z.array(z.string()).optional(),
});

export type UpdateMessageRequestDto = z.infer<typeof UpdateMessageRequestDtoSchema>;

const dateTimeFormat = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
const stringToDate = z.string()
    .regex(dateTimeFormat, { message: 'Invalid date-time format. Expected YYYY-MM-DD HH:mm:ss' })
    .transform((val) => new Date(val));

// Message List Query DTO (cursor-based pagination)
export const MessageListQueryDtoSchema = z.object({
    limit: z.coerce.number().min(1).max(100).default(20),
    cursor: z.uuid().optional(), // dbId for cursor-based pagination
    tags: z.preprocess((val) => {
        if (Array.isArray(val)) {
            return val.filter(item => typeof item === 'string');
        }
        if (typeof val === 'string') {
            // Handle both comma-separated strings and single strings
            return val.includes(',') ? val.split(',').map((s) => s.trim()) : [val];
        }
        return val;
    }, z.array(z.string()).optional()),
    startTime: stringToDate.optional(),
    endTime: stringToDate.optional(),
})
.refine(
    (data) => !data.endTime || !!data.startTime,
    {
        message: 'startTime is required when endTime is provided',
        path: ['startTime'],
    }
)
.refine(
    (data) => !data.startTime || !data.endTime || data.endTime >= data.startTime,
    {
        message: 'endTime must be after startTime',
        path: ['endTime'],
    }
);



export type MessageListQueryDto = z.infer<typeof MessageListQueryDtoSchema>;

// Message Response DTO (for API responses)
export const MessageResponseDtoSchema = z.object({
    dbId: z.uuid(),
    id: z.string(),
    sessionId: z.string(),
    role: MessageRoleSchema,
    content: z.array(z.object({
        type: MessageContentTypeSchema,
        text: z.string().optional(),
        file: z.object({
            bytes: z.string().optional(),
            uri: z.string().optional(),
            metadata: z.record(z.string(), z.unknown()).optional(),
        }).optional(),
        data: z.union([z.record(z.string(), z.unknown()), z.string()]).optional(),
        metadata: z.record(z.string(), z.unknown()).optional(),
    })),
    sender: z.object({
        id: z.string(),
        name: z.string().optional(),
        avatar: z.string().optional(),
        type: z.string().optional(),
    }).nullable(),
    extendedData: z.record(z.string(), z.unknown()).nullable(),
    createdAt: z.string(),
    updatedAt: z.string(),
    userId: z.string().nullable(),
});

export type MessageResponseDto = z.infer<typeof MessageResponseDtoSchema>;

// Message List Response DTO (cursor-based pagination)
export const MessageListResponseDtoSchema = z.object({
    messages: z.array(MessageResponseDtoSchema),
    hasNextPage: z.boolean(),
    nextCursor: z.uuid().optional(),
    limit: z.number().optional(),
});

export type MessageListResponseDto = z.infer<typeof MessageListResponseDtoSchema>;