/**
 * Message Repository
 * 
 * Data access layer for message-related database operations.
 * Simplified to match actual database schema.
 */

import { injectable } from 'tsyringe';
import { desc, lt, eq, and, gte, lte, sql } from 'drizzle-orm';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { message, type MessageInsert } from '@/infrastructure/database/schema/message';
import { session } from '@/infrastructure/database/schema/session';
import { databaseService } from '@/infrastructure/database';
import { logDatabaseOperation } from '@/infrastructure/logger';
import { measureTime } from '@/shared/utils/time';
import { generateUUID } from '@/shared/utils/crypto';
import type { MessageListQueryDto } from '../dto';
import { NotFoundError } from '@/shared/utils';

export interface MessageEntity {
    dbId: string;
    id: string;
    sessionId: string;
    role: 'user' | 'assistant';
    content: Array<{
        type: 'text' | 'file' | 'data';
        text?: string;
        file?: {
            bytes?: string;
            uri?: string;
            metadata?: Record<string, unknown>;
        };
        data?: Record<string, unknown> | string;
        metadata?: Record<string, unknown>;
    }>;
    sender: {
        id: string;
        name?: string;
        avatar?: string;
        type?: string;
        [key: string]: unknown;
    } | null;
    extendedData: Record<string, unknown> | null;
    createdAt: Date;
    updatedAt: Date;
    userId: string | null;
}

export interface CreateMessageEntity extends Omit<MessageInsert, 'dbId' | 'createdAt' | 'updatedAt' | 'content'> {
    content: Array<{
        type: 'text' | 'file' | 'data';
        text?: string;
        file?: {
            bytes?: string;
            uri?: string;
            metadata?: Record<string, unknown>;
        };
        data?: Record<string, unknown> | string;
        metadata?: Record<string, unknown>;
    }>;
}

export interface UpdateMessageEntity extends Omit<MessageInsert, 'createdAt' | 'updatedAt' | 'content'> {
    content: Array<{
        type: 'text' | 'file' | 'data';
        text?: string;
        file?: {
            bytes?: string;
            uri?: string;
            metadata?: Record<string, unknown>;
        };
        data?: Record<string, unknown> | string;
        metadata?: Record<string, unknown>;
    }>;
}

export interface MessageListResult {
    messages: MessageEntity[];
    limit: number;
    hasNextPage: boolean;
    nextCursor?: string | null;
    cursor?: string | null;
}


@injectable()
export class MessageRepository {
    private get db(): PostgresJsDatabase<Record<string, unknown>> {
        return databaseService.getDb();
    }

    /**
     * Find message by ID
     */
    async findById(id: string): Promise<MessageEntity | null> {
        const { result: messages, duration } = await measureTime(async () => {
            return await this.db
                .select()
                .from(message)
                .where(eq(message.dbId, id))
                .limit(1);
        });

        logDatabaseOperation('SELECT', 'message', duration, {
            messageDbId: id,
            found: messages.length > 0,
        });

        return messages[0] ?? null;
    }

    /**
     * Create new message and update session timestamp
     */
    async create(data: CreateMessageEntity): Promise<MessageEntity> {
        const messageData: MessageInsert = {
            ...data,
            id: data.id || generateUUID(),
            createdAt: new Date(),
            updatedAt: new Date(),
        };

        const { result: createdMessage, duration } = await measureTime(async () => {
            return await this.db.transaction(async (tx) => {
                const createdMessages = await tx.insert(message).values(messageData).returning();
                const newMsg = createdMessages[0];

                if (!newMsg) {
                    tx.rollback();
                    throw new Error('Failed to create message - no data returned during transaction');
                }

                await tx
                    .update(session)
                    .set({ updatedAt: messageData.createdAt })
                    .where(eq(session.id, data.sessionId));

                return newMsg;
            });
        });

        logDatabaseOperation('TRANSACTION', 'message/session', duration, {
            operation: 'createMessageAndUpdateSession',
            messageId: createdMessage.id,
            sessionId: data.sessionId,
            role: data.role,
        });

        return createdMessage;
    }

    /**
     * Find messages by session with pagination and filtering
     */
    async findBySession(sessionId: string, query: MessageListQueryDto): Promise<MessageListResult> {
        const { cursor, limit, tags, startTime, endTime } = query;

        const orderDirection = desc;
        const orderColumn = message.updatedAt;

        // Build where conditions
        const conditions = [eq(message.sessionId, sessionId)];

        if (cursor !== undefined && cursor.trim() !== '') {
            const cursorMessage = await this.findById(cursor);
            if (cursorMessage) {
                conditions.push(lt(message.updatedAt, cursorMessage.updatedAt));
            } else {
                throw new NotFoundError('Cursor message not found');
            }
        }

        if (startTime) {
            conditions.push(gte(message.createdAt, startTime));
        }

        if (endTime) {
            conditions.push(lte(message.createdAt, endTime));
        }

        if (tags && tags.length > 0) {
            const tagsArray = Array.isArray(tags) ? tags : [String(tags)];
            conditions.push(sql`${message.tags} && ${sql.param(tagsArray)}`);
        }

        // Get messages with pagination
        const { result: messages, duration: selectDuration } = await measureTime(async () => {
            return await this.db
                .select()
                .from(message)
                .where(and(...conditions))
                .orderBy(orderDirection(orderColumn))
                .limit(limit + 1); // +1 to check if there is more data
        });


        const hasNextPage = messages.length > limit;
        const resultMessages = hasNextPage ? messages.slice(0, limit) : messages;

        // 获取下一页的cursor
        const nextCursor =
            hasNextPage && messages[limit-1] ? messages[limit-1]!.dbId : null;

        logDatabaseOperation('SELECT', 'message', selectDuration, {
            sessionId: sessionId,
            cursor: cursor,
            limit: limit,
            hasNextPage,
            nextCursor: nextCursor ?? null,
            count: resultMessages.length,
        });
        return {
            messages: resultMessages.reverse(),
            limit: limit,
            hasNextPage,
            nextCursor,
        };
    }

    /**
     * Delete message by ID
     */
    async delete(id: string): Promise<boolean> {
        const { result: deletedMessages, duration } = await measureTime(async () => {
            return await this.db
                .delete(message)
                .where(eq(message.dbId, id))
                .returning();
        });

        logDatabaseOperation('DELETE', 'message', duration, {
            messageDbId: id,
            deleted: deletedMessages.length > 0,
        });

        return deletedMessages.length > 0;
    }

    /**
     * Verify session exists and user has access
     */
    async verifySessionAccess(sessionId: string, userId: string): Promise<boolean> {
        const { result: sessions, duration } = await measureTime(async () => {
            return await this.db
                .select({ id: session.id })
                .from(session)
                .where(and(
                    eq(session.id, sessionId),
                    eq(session.userId, userId)
                ))
                .limit(1);
        });

        logDatabaseOperation('SELECT', 'session', duration, {
            sessionId,
            userId,
            hasAccess: sessions.length > 0,
        });

        return sessions.length > 0;
    }

    async hasMessageBySession(sessionId: string): Promise<boolean> {
        const { result: messages, duration } = await measureTime(async () => {
            return await this.db
                .select({ id: message.id })
                .from(message)
                .where(eq(message.sessionId, sessionId))
                .limit(1);
        });

        logDatabaseOperation('SELECT', 'message', duration, {
            sessionId,
            hasMessage: messages.length > 0,
        });

        return messages.length > 0;
    }

    async update(data: UpdateMessageEntity): Promise<MessageEntity> {
        if (data.dbId === undefined) {
            throw new Error('Message dbId is required for update operation.');
        }
        const { result: updatedMessages, duration } = await measureTime(async () => {
            return await this.db.update(message).set(data).where(eq(message.dbId, data.dbId!)).returning();
        });

        if (updatedMessages.length === 0) {
            throw new NotFoundError(`Message with dbId '${data.dbId}' not found for update.`);
        }

        const updatedMessage = updatedMessages[0]!;

        logDatabaseOperation('UPDATE', 'message', duration, {
            messageDbId: updatedMessage.dbId,
        });
        return updatedMessage;
    }
} 