/**
 * Application Entry Point
 * 
 * This is the main entry point for the xui-app-server application.
 * It initializes and starts the HTTP server.
 */

// 必须在所有其他导入之前导入reflect-metadata
import 'reflect-metadata';

import { startServer } from '@/app/server';
import { getEnvironment, initializeNacosConfig } from '@/shared/utils/env';
import { Logger } from '@/infrastructure/logger/winston-logger';

/**
 * Start the application
 */
async function main(): Promise<void> {
    try {
        Logger.info('Starting xui-app-server...');
        
        // Initialize Nacos configuration first
        await initializeNacosConfig();
        
        // Get environment configuration after Nacos initialization
        const env = getEnvironment();
        Logger.info(`Environment: ${JSON.stringify(env)}`);
        
        await startServer();
    } catch (error) {
        Logger.error('Failed to start application', {}, error as Error);
        process.exit(1);
    }
}

// Start the application
void main();
