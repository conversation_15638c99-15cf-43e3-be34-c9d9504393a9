/**
 * Nacos Configuration Service
 * 
 * Service for managing Nacos configuration center integration.
 */

import { NacosConfigClient } from 'nacos';
import type { INacosConfigService, NacosConfig, NacosConfigItem } from '@/shared/types';
import { Logger } from '@/infrastructure/logger';

export class NacosConfigService implements INacosConfigService {
    private readonly client: NacosConfigClient;
    private readonly defaultGroup: string = 'DEFAULT_GROUP';

    constructor(config: NacosConfig) {
        const clientOptions: Record<string, string | number> = {
            serverAddr: config.serverAddr,
            requestTimeout: config.requestTimeout ?? 6000,
        };

        // 只添加非undefined的可选属性
        if (config.namespace !== undefined) {
            clientOptions['namespace'] = config.namespace;
        }
        if (config.username !== undefined) {
            clientOptions['username'] = config.username;
        }
        if (config.password !== undefined) {
            clientOptions['password'] = config.password;
        }
        if (config.endpoint !== undefined) {
            clientOptions['endpoint'] = config.endpoint;
        }
        if (config.accessKey !== undefined) {
            clientOptions['accessKey'] = config.accessKey;
        }
        if (config.secretKey !== undefined) {
            clientOptions['secretKey'] = config.secretKey;
        }

        this.client = new NacosConfigClient(clientOptions);
    }

    /**
     * 获取Nacos客户端实例
     */
    getClient(): NacosConfigClient {
        return this.client;
    }

    /**
     * 获取配置
     * @param dataId 配置ID
     * @param group 配置组，默认为DEFAULT_GROUP
     */
    async getConfig(dataId: string, group?: string): Promise<string> {
        try {
            const content = await this.client.getConfig(dataId, group ?? this.defaultGroup);
            Logger.info(`Successfully retrieved config: ${dataId} from group: ${group ?? this.defaultGroup}`);
            return content;
        } catch (error) {
            Logger.error(
                `Failed to get config ${dataId} from group ${group ?? this.defaultGroup}:`, 
                {}, 
                error as Error
            );
            throw error;
        }
    }

    /**
     * 发布配置
     * @param dataId 配置ID
     * @param group 配置组
     * @param content 配置内容
     */
    async publishConfig(dataId: string, group: string, content: string): Promise<boolean> {
        try {
            const result = await this.client.publishSingle(dataId, group, content);
            Logger.info(`Successfully published config: ${dataId} to group: ${group}`);
            return result;
        } catch (error) {
            Logger.error(`Failed to publish config ${dataId} to group ${group}:`, {}, error as Error);
            throw error;
        }
    }

    /**
     * 删除配置
     * @param dataId 配置ID
     * @param group 配置组，默认为DEFAULT_GROUP
     */
    async removeConfig(dataId: string, group?: string): Promise<boolean> {
        try {
            await this.client.remove(dataId, group ?? this.defaultGroup);
            Logger.info(`Successfully removed config: ${dataId} from group: ${group ?? this.defaultGroup}`);
            return true;
        } catch (error) {
            Logger.error(
                `Failed to remove config ${dataId} from group ${group ?? this.defaultGroup}:`, 
                {}, 
                error as Error
            );
            throw error;
        }
    }

    /**
     * 订阅配置变更
     * @param config 配置项
     * @param listener 监听器回调
     */
    subscribe(config: NacosConfigItem, listener: (content: string) => void): void {
        try {
            this.client.subscribe({
                dataId: config.dataId,
                group: config.group ?? this.defaultGroup,
            }, listener);
            Logger.info(
                `Successfully subscribed to config: ${config.dataId} in group: ${config.group ?? this.defaultGroup}`
            );
        } catch (error) {
            Logger.error(`Failed to subscribe to config ${config.dataId}:`, {}, error as Error);
            throw error;
        }
    }

    /**
     * 取消订阅配置变更
     * @param config 配置项
     * @param listener 监听器回调（可选）
     */
    unsubscribe(config: NacosConfigItem, listener?: (content: string) => void): void {
        try {
            this.client.unSubscribe({
                dataId: config.dataId,
                group: config.group ?? this.defaultGroup,
            }, listener);
            Logger.info(
                `Successfully unsubscribed from config: ${config.dataId} in group: ${config.group ?? this.defaultGroup}`
            );
        } catch (error) {
            Logger.error(`Failed to unsubscribe from config ${config.dataId}:`, {}, error as Error);
            throw error;
        }
    }
} 