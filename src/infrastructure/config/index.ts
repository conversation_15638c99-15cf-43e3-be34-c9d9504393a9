/**
 * 基础设施配置
 * 
 * 基础设施层的中央配置管理，支持Nacos配置中心。
 */

import { getEnvironment } from '@/shared/utils/env';

/**
 * 获取数据库配置（支持Nacos配置覆盖）
 */
export function getDatabaseConfig(): Record<string, unknown> {
    const env = getEnvironment();
    return {
        host: env.DB_HOST,
        port: env.DB_PORT,
        database: env.DB_NAME,
        user: env.DB_USER,
        password: env.DB_PASSWORD,
        ssl: env.DB_SSL,
        max: parseInt(process.env['DB_MAX_CONNECTIONS'] ?? '20', 10),
        idleTimeoutMillis: parseInt(process.env['DB_IDLE_TIMEOUT'] ?? '30000', 10),
        connectionTimeoutMillis: parseInt(process.env['DB_CONNECTION_TIMEOUT'] ?? '10000', 10),
    } as const;
}

/**
 * 获取服务器配置（支持Nacos配置覆盖）
 */
export function getServerConfig() : Record<string, unknown> {
    const env = getEnvironment();
    return {
        port: env.PORT,
        host: env.HOST,
    } as const;
}

/**
 * 获取CORS配置（支持Nacos配置覆盖）
 */
export function getCorsConfig() : Record<string, unknown> {
    const env = getEnvironment();
    return {
        origin: env.CORS_ORIGIN,
        credentials: env.CORS_CREDENTIALS,
    } as const;
}

/**
 * 获取日志配置（支持Nacos配置覆盖）
 */
export function getLoggingConfig() : Record<string, unknown> {
    const env = getEnvironment();
    const logDir = env.LOG_DIR;
    return {
        level: env.LOG_LEVEL,
        format: env.LOG_FORMAT,
        errorLogFile: `${logDir}/error.log`,
        combinedLogFile: `${logDir}/combined.log`,
    } as const;
}

/**
 * 获取安全配置（支持Nacos配置覆盖）
 */
export function getSecurityConfig() : Record<string, unknown> {
    const env = getEnvironment();
    return {
        bcryptRounds: env.BCRYPT_ROUNDS,
        rateLimitWindowMs: env.RATE_LIMIT_WINDOW_MS,
        rateLimitMaxRequests: env.RATE_LIMIT_MAX_REQUESTS,
    } as const;
}

/**
 * 获取优雅关闭配置（支持Nacos配置覆盖）
 */
export function getGracefulShutdownConfig() : Record<string, unknown> {
    const env = getEnvironment();
    return {
        timeout: env.GRACEFUL_SHUTDOWN_TIMEOUT,
    } as const;
}

/**
 * 获取指标配置（支持Nacos配置覆盖）
 */
export function getMetricsConfig() : Record<string, unknown> {
    const env = getEnvironment();
    return {
        enabled: env.ENABLE_METRICS,
        port: env.METRICS_PORT,
    } as const;
}

/**
 * 获取Redis配置（支持Nacos配置覆盖）
 */
export function getRedisConfig() : Record<string, unknown> | undefined {
    const env = getEnvironment();
    return (env.REDIS_URL !== undefined && env.REDIS_URL.trim() !== '') ? {
        url: env.REDIS_URL,
        password: env.REDIS_PASSWORD,
    } : undefined;
}

// 注意：为了支持Nacos配置覆盖，建议使用函数形式的配置
// 例如：getDatabaseConfig(), getServerConfig() 等

/**
 * Langfuse配置
 */
export const langfuseConfig = {
    publicKey: process.env['LANGFUSE_PUBLIC_KEY'],
    secretKey: process.env['LANGFUSE_SECRET_KEY'],
    baseUrl: process.env['LANGFUSE_HOST'] ?? 'https://langfuse.yxt.com.cn',
    enabled: !!(
        process.env['LANGFUSE_PUBLIC_KEY'] !== undefined 
        && process.env['LANGFUSE_PUBLIC_KEY'].trim() !== ''
        && process.env['LANGFUSE_SECRET_KEY'] !== undefined
        && process.env['LANGFUSE_SECRET_KEY'].trim() !== ''
    ),
} as const;

// Nacos配置相关函数直接从 @/shared/utils/env 导入，避免循环依赖

/**
 * 获取统一应用配置（支持Nacos配置覆盖）
 */
export function getConfig() : Record<string, unknown> {
    return {
        server: getServerConfig(),
        database: getDatabaseConfig(),
        cors: getCorsConfig(),
        logging: getLoggingConfig(),
        security: getSecurityConfig(),
        gracefulShutdown: getGracefulShutdownConfig(),
        metrics: getMetricsConfig(),
        redis: getRedisConfig(),
        langfuse: langfuseConfig,
    } as const;
}

// 注意：为了支持Nacos配置覆盖，请使用 getConfig() 函数而不是 config 常量 