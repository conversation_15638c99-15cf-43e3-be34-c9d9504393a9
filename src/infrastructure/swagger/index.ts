/**
 * Swagger (OpenAPI) Configuration
 *
 * Sets up Swagger for API documentation.
 */

import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import type { Express } from 'express';
import { getServerConfig } from '@/infrastructure/config';
import { logInfo } from '@/infrastructure/logger';
import { openApiDefinition } from './openapi-definition';

/**
 * Setup Swagger UI
 */
export const setupSwagger = (app: Express): void => {
    const serverConfig = getServerConfig();
    const host = serverConfig['host'] as string;
    const port = serverConfig['port'] as number;

    // 强制使用HTTP协议
    const serverUrl = `http://${host}:${port}`;

    const swaggerSpec = swaggerJsdoc({
        definition: {
            ...openApiDefinition,
            servers: [
                {
                    url: serverUrl,
                    description: 'Server',
                }
            ]
        },
        apis: ['./src/app/routes/*.ts'],
    });

    // 为Swagger UI路由禁用某些安全头部
    app.use('/api-docs', (_req, res, next) => {
        // 移除可能导致问题的安全头部
        res.removeHeader('Cross-Origin-Opener-Policy');
        res.removeHeader('Cross-Origin-Embedder-Policy');
        res.removeHeader('Origin-Agent-Cluster');

        // 设置适合Swagger UI的CSP头部
        res.setHeader('Content-Security-Policy',
            "default-src 'self'; " +
            "style-src 'self' 'unsafe-inline'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
            "img-src 'self' data: https:; " +
            "connect-src 'self';"
        );

        next();
    });

    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
        swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
        },
        customCss: '.swagger-ui .topbar { display: none }',
        customSiteTitle: 'XUI App Server API Documentation'
    }));


    
    logInfo(`Swagger docs available at /api-docs (${serverUrl}/api-docs)`);
};
