/**
 * Swagger (OpenAPI) Configuration
 * 
 * Sets up Swagger for API documentation.
 */

import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import type { Express, Request, Response } from 'express';
import { logInfo } from '@/infrastructure/logger';
import { openApiDefinition } from './openapi-definition';

/**
 * Setup Swagger UI
 */
export const setupSwagger = (app: Express): void => {
    const swaggerSpec = swaggerJsdoc({
        definition: {
            ...openApiDefinition,
            // 不预设服务器URL，让Swagger UI自动使用当前页面的协议和地址
        },
        apis: ['./src/app/routes/*.ts', './src/modules/**/*.ts', './src/shared/types/*.ts'],
    });

    // 创建自定义的Swagger UI设置函数，确保使用正确的协议
    const customSwaggerSetup = (req: Request, res: Response) => {
        const protocol = req.secure ? 'https' : 'http';
        const host = req.get('host');
        const baseUrl = `${protocol}://${host}`;

        // 动态生成Swagger UI HTML，确保所有资源使用正确的协议
        const html = `
<!DOCTYPE html>
<html>
<head>
    <title>XUI App Server API Documentation</title>
    <link rel="stylesheet" type="text/css" href="${baseUrl}/api-docs/swagger-ui.css" />
    <style>
        .swagger-ui .topbar { display: none }
        .swagger-ui .scheme-container { display: none }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="${baseUrl}/api-docs/swagger-ui-bundle.js"></script>
    <script src="${baseUrl}/api-docs/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            SwaggerUIBundle({
                url: '${baseUrl}/api-docs/swagger.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                persistAuthorization: true,
                displayRequestDuration: true,
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                tryItOutEnabled: true
            });
        };
    </script>
</body>
</html>`;

        res.send(html);
    };

    // 添加协议检测中间件，确保Swagger UI使用正确的协议
    app.use('/api-docs', (req, _res, next) => {
        // 记录当前请求的协议信息，用于调试
        const protocol = req.secure ? 'https' : 'http';
        logInfo(`Swagger request protocol: ${protocol}, secure: ${req.secure}`);
        next();
    });

    // 使用自定义设置函数替代标准的swagger-ui设置
    app.use('/api-docs', swaggerUi.serve);
    app.get('/api-docs', customSwaggerSetup);

    // 提供swagger.json端点
    app.get('/api-docs/swagger.json', (_req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(swaggerSpec);
    });

    logInfo('Swagger docs available at /api-docs');
};
