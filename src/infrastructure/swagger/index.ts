/**
 * Swagger (OpenAPI) Configuration
 * 
 * Sets up Swagger for API documentation.
 */

import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import type { Express } from 'express';
import { logInfo } from '@/infrastructure/logger';
import { openApiDefinition } from './openapi-definition';

/**
 * Setup Swagger UI
 */
export const setupSwagger = (app: Express): void => {
    const swaggerSpec = swaggerJsdoc({
        definition: {
            ...openApiDefinition,
            // 不预设服务器URL，让Swagger UI自动使用当前页面的协议和地址
        },
        apis: ['./src/app/routes/*.ts', './src/modules/**/*.ts', './src/shared/types/*.ts'],
    });

    // Swagger UI配置 - 使用相对路径，自动适配当前协议
    const swaggerUiOptions = {
        swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
            // 使用相对路径，自动继承当前页面的协议
            url: '/api-docs/swagger.json',
        },
        customCss: `
            .swagger-ui .topbar { display: none }
            .swagger-ui .scheme-container { display: none }
        `,
        customSiteTitle: 'XUI App Server API Documentation',
    };

    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));

    // 提供swagger.json端点
    app.get('/api-docs/swagger.json', (_req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(swaggerSpec);
    });

    logInfo('Swagger docs available at /api-docs');
};

