/**
 * Swagger (OpenAPI) Configuration
 * 
 * Sets up Swagger for API documentation.
 */

import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import type { Express, Request, Response } from 'express';
import { logInfo } from '@/infrastructure/logger';
import { openApiDefinition } from './openapi-definition';

/**
 * Setup Swagger UI
 */
export const setupSwagger = (app: Express): void => {
    const swaggerSpec = swaggerJsdoc({
        definition: {
            ...openApiDefinition,
            // 动态设置服务器URL，根据请求协议自动配置
            servers: [], // 先设置为空，在请求时动态添加
        },
        apis: ['./src/app/routes/*.ts', './src/modules/**/*.ts', './src/shared/types/*.ts'],
    });

    // 创建自定义的Swagger UI设置函数，使用相对路径避免协议问题
    const customSwaggerSetup = (req: Request, res: Response): void => {
        const protocol = req.secure ? 'https' : 'http';
        const host = req.get('host');

        // 动态更新swagger spec的服务器配置
        const dynamicSwaggerSpec = {
            ...swaggerSpec,
            servers: [
                {
                    url: `${protocol}://${host}`,
                    description: process.env['NODE_ENV'] === 'production' ? 'Production server' : 'Development server',
                }
            ]
        };

        // 使用相对路径生成HTML，避免协议冲突
        const html = `
<!DOCTYPE html>
<html>
<head>
    <title>XUI App Server API Documentation</title>
    <link rel="stylesheet" type="text/css" href="./swagger-ui.css" />
    <style>
        .swagger-ui .topbar { display: none }
        .swagger-ui .scheme-container { display: none }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="./swagger-ui-bundle.js"></script>
    <script src="./swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            SwaggerUIBundle({
                spec: ${JSON.stringify(dynamicSwaggerSpec)},
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                persistAuthorization: true,
                displayRequestDuration: true,
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                tryItOutEnabled: true
            });
        };
    </script>
</body>
</html>`;

        res.send(html);
    };

    // 添加协议检测中间件，确保Swagger UI使用正确的协议
    app.use('/api-docs', (req, _res, next) => {
        // 记录当前请求的协议信息，用于调试
        const protocol = req.secure ? 'https' : 'http';
        logInfo(`Swagger request protocol: ${protocol}, secure: ${req.secure}`);
        next();
    });

    // 使用自定义设置函数替代标准的swagger-ui设置
    app.use('/api-docs', swaggerUi.serve);
    app.get('/api-docs', customSwaggerSetup);

    // 提供swagger.json端点
    app.get('/api-docs/swagger.json', (_req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(swaggerSpec);
    });

    logInfo('Swagger docs available at /api-docs');
};
