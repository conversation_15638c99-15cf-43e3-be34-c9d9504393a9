/**
 * Swagger (OpenAPI) Configuration
 * 
 * Sets up Swagger for API documentation.
 */

import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import type { Express } from 'express';
import { getServerConfig } from '@/infrastructure/config';
import { logInfo } from '@/infrastructure/logger';
import { openApiDefinition } from './openapi-definition';

/**
 * Setup Swagger UI
 */
export const setupSwagger = (app: Express): void => {
    const serverConfig = getServerConfig();

    // 确定协议 - 支持多种配置方式
    const protocol = getSwaggerProtocol();

    // 构建服务器URL - 支持外部访问地址配置
    const host = serverConfig['host'] as string;
    const port = serverConfig['port'] as number;
    const serverUrl = `${protocol}://${host}:${port}`;

    const swaggerSpec = swaggerJsdoc({
        definition: {
            ...openApiDefinition,
            servers: [
                {
                    url: serverUrl,
                    description: process.env['NODE_ENV'] === 'production' ? 'Production server' : 'Development server',
                },
            ],
        },
        apis: ['./src/app/routes/*.ts', './src/modules/**/*.ts', './src/shared/types/*.ts'],
    });

    // Swagger UI配置
    const swaggerUiOptions = {
        swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
            // 确保使用正确的协议加载资源
            url: `${serverUrl}/api-docs/swagger.json`,
        },
        customCss: `
            .swagger-ui .topbar { display: none }
            .swagger-ui .scheme-container { display: none }
        `,
        customSiteTitle: 'XUI App Server API Documentation',
        // 确保静态资源使用正确的协议
        customCssUrl: undefined,
        customJs: undefined,
        customfavIcon: undefined,
    };

    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));

    // 提供swagger.json端点
    app.get('/api-docs/swagger.json', (_req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(swaggerSpec);
    });

    logInfo(`Swagger docs available at /api-docs (${serverUrl}/api-docs)`);
};

/**
 * 获取Swagger协议配置
 */
function getSwaggerProtocol(): string {
    // 1. 优先使用明确指定的协议
    const swaggerProtocol = process.env['SWAGGER_PROTOCOL'];
    if (swaggerProtocol !== undefined && swaggerProtocol.trim() !== '') {
        return swaggerProtocol;
    }

    // 2. 检查是否在反向代理后面
    if (process.env['BEHIND_PROXY'] === 'true') {
        return process.env['PROXY_PROTOCOL'] ?? 'https';
    }

    // 3. 根据环境判断
    if (process.env['NODE_ENV'] === 'production') {
        return 'https';
    }

    // 4. 默认使用http
    return 'http';
}