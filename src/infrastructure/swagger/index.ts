/**
 * Swagger (OpenAPI) Configuration
 *
 * Sets up Swagger for API documentation.
 */

import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import type { Express, Request, Response } from 'express';
import { getServerConfig } from '@/infrastructure/config';
import { logInfo } from '@/infrastructure/logger';
import { openApiDefinition } from './openapi-definition';

/**
 * Setup Swagger UI
 */
export const setupSwagger = (app: Express): void => {
    const serverConfig = getServerConfig();
    const host = serverConfig['host'] as string;
    const port = serverConfig['port'] as number;

    // 强制使用HTTP协议
    const serverUrl = `http://${host}:${port}`;

    const swaggerSpec = swaggerJsdoc({
        definition: {
            ...openApiDefinition,
            servers: [
                {
                    url: serverUrl,
                    description: 'Server',
                }
            ]
        },
        apis: ['./src/app/routes/*.ts', './src/modules/**/*.ts', './src/shared/types/*.ts'],
    });

    // 简化的Swagger UI配置
    const swaggerUiOptions = {
        swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
            url: '/api-docs/swagger.json',
        },
        customCss: `
            .swagger-ui .topbar { display: none }
            .swagger-ui .scheme-container { display: none }
        `,
        customSiteTitle: 'XUI App Server API Documentation',
    };

    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));

    // 提供swagger.json端点
    app.get('/api-docs/swagger.json', (_req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(swaggerSpec);
    });

    logInfo(`Swagger docs available at /api-docs (${serverUrl}/api-docs)`);
};
