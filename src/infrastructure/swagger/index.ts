/**
 * Swagger (OpenAPI) Configuration
 * 
 * Sets up Swagger for API documentation.
 */

import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import type { Express } from 'express';
// import { getServerConfig } from '@/infrastructure/config';
import { logInfo } from '@/infrastructure/logger';
import { openApiDefinition } from './openapi-definition';

/**
 * Setup Swagger UI
 */
export const setupSwagger = (app: Express): void => {
    // const serverConfig = getServerConfig();
    const swaggerSpec = swaggerJsdoc({
        definition: {
            ...openApiDefinition,
            // servers: [
            //     {
            //         url: `http://${serverConfig['host']}:${serverConfig['port']}`,
            //         description: 'Development server',
            //     },
            // ],
        },
        apis: ['./src/app/routes/*.ts', './src/modules/**/*.ts', './src/shared/types/*.ts'],
    });

    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
        swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
        },
        customCss: '.swagger-ui .topbar { display: none }',
        customSiteTitle: 'XUI App Server API Documentation'
    }));
    
    logInfo(`Swagger docs available at /api-docs`);
}; 