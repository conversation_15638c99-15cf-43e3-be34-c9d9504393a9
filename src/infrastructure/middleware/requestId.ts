/**
 * Request ID Middleware
 * 
 * Generates unique request IDs for tracking and logging.
 */

import { Request, Response, NextFunction } from 'express';
import { generateUUID } from '@/shared/utils/crypto';
import type { AuthenticatedRequest } from '@/shared/types';

/**
 * Request ID middleware
 */
export function requestIdMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
): void {
    const authReq = req as AuthenticatedRequest;

    // Check if request ID already exists (from upstream)
    const existingId = authReq.headers['x-request-id'];
    const requestId = (typeof existingId === 'string' && existingId.trim() !== '') 
        ? existingId.trim() 
        : generateUUID();

    // Add to request object
    authReq.requestId = requestId;

    // Add to response headers
    res.setHeader('X-Request-ID', requestId);

    next();
}
