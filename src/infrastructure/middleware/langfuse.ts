/**
 * Langfuse 中间件
 *
 * 提供 Express 中间件来自动追踪 HTTP 请求和响应
 * 集成 Langfuse 可观测性功能
 */
import { Request, Response, NextFunction } from 'express';
import { generateUUID } from '@/shared/utils/crypto';
import getLangfuseService from '@/infrastructure/logger/langfuse';
import { Logger } from '@/infrastructure/logger/winston-logger';
import type { JsonValue } from '@/shared/types';
import { getDateTime } from '@/shared/utils';

const langfuse = getLangfuseService();

/**
 * Langfuse Trace 接口
 */
interface LangfuseTrace {
    id: string;
    update(params: {
        output?: Record<string, JsonValue>;
        metadata?: Record<string, JsonValue>;
        name?: string;
        tags?: string[];
        [key: string]: unknown;
    }): void;
    metadata?: Record<string, JsonValue>;
}

/**
 * 扩展 Request 接口以包含 Langfuse 追踪信息
 */
declare global {
    namespace Express {
        interface Request {
            langfuseTrace?: LangfuseTrace;
            langfuseTraceId?: string;
        }
    }
}

/**
 * Langfuse 追踪中间件选项
 */
export interface LangfuseMiddlewareOptions {
    /** 是否追踪请求体 */
    traceRequestBody?: boolean;
    /** 是否追踪响应体 */
    traceResponseBody?: boolean;
    /** 是否追踪请求头 */
    traceRequestHeaders?: boolean;
    /** 是否追踪响应头 */
    traceResponseHeaders?: boolean;
    /** 排除的路径模式 */
    excludePaths?: (string | RegExp)[];
    /** 排除的 HTTP 方法 */
    excludeMethods?: string[];
    /** 自定义追踪名称生成器 */
    generateTraceName?: (req: Request) => string;
    /** 自定义元数据生成器 */
    generateMetadata?: (req: Request, res: Response) => Record<string, JsonValue>;
}

/**
 * 默认中间件选项
 */
const defaultOptions: LangfuseMiddlewareOptions = {
    traceRequestBody: true,
    traceResponseBody: false, // 默认不追踪响应体以避免大量数据
    traceRequestHeaders: false,
    traceResponseHeaders: false,
    excludePaths: ['/health', '/metrics', '/favicon.ico'],
    excludeMethods: ['OPTIONS'],
    generateTraceName: (req: Request) => `${req.method} ${req.path}`,
    generateMetadata: (req: Request) => ({
        method: req.method,
        path: req.path,
        userAgent: req.get('User-Agent') ?? null,
        ip: req.ip ?? null,
        timestamp: getDateTime(new Date())
    })
};

/**
 * 检查路径是否应该被排除
 */
function shouldExcludePath(path: string, excludePaths: (string | RegExp)[]): boolean {
    return excludePaths.some(pattern => {
        if (typeof pattern === 'string') {
            return path === pattern || path.startsWith(pattern);
        }
        return pattern.test(path);
    });
}

/**
 * 安全地序列化对象，避免循环引用
 */
function safeStringify(obj: unknown): JsonValue {
    if (obj === null) {
        return null;
    }

    if (obj === undefined) {
        return null;
    }

    if (typeof obj !== 'object') {
        return obj as JsonValue;
    }

    try {
        return JSON.parse(JSON.stringify(obj)) as JsonValue;
    } catch {
        return '[Circular Reference or Non-serializable Object]';
    }
}

/**
 * 检查对象是否为有效的 Langfuse trace
 */
function isValidTrace(trace: unknown): trace is LangfuseTrace {
    return trace !== null && 
           trace !== undefined && 
           typeof trace === 'object' && 
           'update' in trace &&
           typeof (trace as LangfuseTrace).update === 'function';
}

/**
 * 创建 Langfuse 追踪中间件
 */
export function createLangfuseMiddleware(
    options: LangfuseMiddlewareOptions = {}
): (req: Request, res: Response, next: NextFunction) => void {
    const opts = { ...defaultOptions, ...options };

    return (req: Request, res: Response, next: NextFunction): void => {
        // 检查 Langfuse 是否启用
        if (!langfuse.isEnabled()) {
            return next();
        }

        // 检查是否应该排除此请求
        const shouldExcludeMethod = opts.excludeMethods?.includes(req.method) === true;
        const shouldExcludePathResult = shouldExcludePath(req.path, opts.excludePaths ?? []);

        if (shouldExcludeMethod || shouldExcludePathResult) {
            return next();
        }

        try {
            // 生成追踪 ID
            const traceId = generateUUID();
            req.langfuseTraceId = traceId;

            // 准备输入数据
            const input: Record<string, JsonValue> = {
                method: req.method,
                url: req.url,
                path: req.path,
                query: safeStringify(req.query)
            };

            if (opts.traceRequestBody === true && req.body !== undefined && req.body !== null) {
                input['body'] = safeStringify(req.body);
            }

            if (opts.traceRequestHeaders === true) {
                input['headers'] = safeStringify(req.headers);
            }

            // 创建追踪
            const traceName = opts.generateTraceName?.(req) ?? defaultOptions.generateTraceName!(req);
            const trace = langfuse.createTrace(traceName, input, {
                traceId,
                ...opts.generateMetadata?.(req, res)
            }) as LangfuseTrace;

            req.langfuseTrace = trace;

            // 记录开始时间
            const startTime = Date.now();

            // 监听响应完成
            res.on('finish', () => {
                try {
                    const duration = Date.now() - startTime;
                    
                    // 准备输出数据
                    const output: Record<string, JsonValue> = {
                        statusCode: res.statusCode,
                        statusMessage: res.statusMessage,
                        duration
                    };

                    if (opts.traceResponseHeaders === true) {
                        output['headers'] = safeStringify(res.getHeaders());
                    }

                    // 更新追踪
                    if (isValidTrace(trace)) {
                        try {
                            trace.update({
                                output,
                                metadata: {
                                    ...trace.metadata,
                                    duration,
                                    statusCode: res.statusCode,
                                    success: res.statusCode < 400
                                }
                            });
                        } catch (traceError) {
                            Logger.error('Error updating trace', { traceError });
                        }
                    }

                    Logger.debug('Langfuse trace completed', {
                        traceId,
                        method: req.method,
                        path: req.path,
                        statusCode: res.statusCode,
                        duration
                    });

                } catch (error) {
                    Logger.error('Error updating Langfuse trace', { error, traceId });
                }
            });

            // 监听响应错误
            res.on('error', (error) => {
                try {
                    if (isValidTrace(trace)) {
                        trace.update({
                            output: {
                                error: error.message,
                                statusCode: res.statusCode || 500
                            },
                            metadata: {
                                ...trace.metadata,
                                error: true,
                                errorMessage: error.message
                            }
                        });
                    }

                    Logger.error('Langfuse trace error', { error: error.message, traceId });
                } catch (updateError) {
                    const errorMessage = updateError instanceof Error ? updateError.message : 'Unknown error';
                    Logger.error('Error updating Langfuse trace with error', { updateError: errorMessage, traceId });
                }
            });

        } catch (error) {
            Logger.error('Error creating Langfuse trace', { error });
            next();
        }

        next();
    };
}

/**
 * 默认 Langfuse 中间件
 */
export const langfuseMiddleware = createLangfuseMiddleware();

/**
 * 用于 API 路由的 Langfuse 中间件（包含更多追踪信息）
 */
export const apiLangfuseMiddleware = createLangfuseMiddleware({
    traceRequestBody: true,
    traceResponseBody: false,
    traceRequestHeaders: false,
    traceResponseHeaders: false,
    excludePaths: ['/health', '/metrics'],
    generateTraceName: (req: Request) => `API ${req.method} ${req.path}`,
    generateMetadata: (req: Request) => ({
        method: req.method,
        path: req.path,
        userAgent: req.get('User-Agent') ?? null,
        ip: req.ip ?? null,
        timestamp: getDateTime(new Date()),
        apiVersion: req.get('API-Version') ?? 'v1'
    })
});

/**
 * 用于健康检查的轻量级中间件
 */
export const healthLangfuseMiddleware = createLangfuseMiddleware({
    traceRequestBody: false,
    traceResponseBody: false,
    traceRequestHeaders: false,
    traceResponseHeaders: false,
    excludePaths: [],
    generateTraceName: (req: Request) => `Health ${req.method} ${req.path}`,
    generateMetadata: () => ({
        type: 'health-check',
        timestamp: getDateTime(new Date())
    })
});

/**
 * 获取当前请求的 Langfuse 追踪
 */
export function getCurrentTrace(req: Request): LangfuseTrace | undefined {
    return req.langfuseTrace;
}

/**
 * 获取当前请求的 Langfuse 追踪 ID
 */
export function getCurrentTraceId(req: Request): string | undefined {
    return req.langfuseTraceId;
}
