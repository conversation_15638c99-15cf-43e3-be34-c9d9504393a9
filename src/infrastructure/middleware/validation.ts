/**
 * 验证中间件
 * 
 * 使用Zod模式进行请求验证。
 */

import { Request, Response, NextFunction } from 'express';
import { z } from 'zod/v4';
import { StatusCodes } from 'http-status-codes';
import { createErrorResponse } from '@/shared/utils/response-formatter';
import { Logger } from '@/infrastructure/logger/winston-logger';

/**
 * 验证请求体是否符合模式
 */
export function validateBody<T>(schema: z.ZodType<T>) {
    return (req: Request, res: Response, next: NextFunction): void => {
        const result = schema.safeParse(req.body);
        
        if (result.success) {
            req.body = result.data;
            next();
        } else {
            Logger.warn('Request body validation failed', {
                path: req.path,
                method: req.method,
                errors: result.error.issues,
                body: req.body,
            });

            const errorResponse = createErrorResponse(
                'Validation failed',
                {
                    code: 'VALIDATION_ERROR',
                    details: result.error.issues.map((issue) => ({
                        field: issue.path.join('.'),
                        message: issue.message,
                        value: issue.code,
                    })),
                }
            );

            res.status(StatusCodes.BAD_REQUEST).json(errorResponse);
        }
    };
}

/**
 * 验证请求查询参数是否符合模式
 */
export function validateQuery<T>(schema: z.ZodType<T>) {
    return (req: Request, res: Response, next: NextFunction): void => {
        const result = schema.safeParse(req.query);
        
        if (result.success) {
            // 类型安全的方式更新 query 参数
            Object.assign(req.query, result.data);
            next();
        } else {
            Logger.warn('Query validation failed', {
                path: req.path,
                method: req.method,
                errors: result.error.issues,
                query: req.query,
            });

            const errorResponse = createErrorResponse(
                'Query validation failed',
                {
                    code: 'VALIDATION_ERROR',
                    details: result.error.issues.map((issue) => ({
                        field: issue.path.join('.'),
                        message: issue.message,
                        value: issue.code,
                    })),
                }
            );

            res.status(StatusCodes.BAD_REQUEST).json(errorResponse);
        }
    };
}

/**
 * 验证请求参数是否符合模式
 */
export function validateParams<T>(schema: z.ZodType<T>) {
    return (req: Request, res: Response, next: NextFunction): void => {
        const result = schema.safeParse(req.params);
        
        if (result.success) {
            // 类型安全的方式更新 params 参数
            Object.assign(req.params, result.data);
            next();
        } else {
            Logger.warn('Params validation failed', {
                path: req.path,
                method: req.method,
                errors: result.error.issues,
                params: req.params,
            });

            const errorResponse = createErrorResponse(
                'Parameter validation failed',
                {
                    code: 'VALIDATION_ERROR',
                    details: result.error.issues.map((issue) => ({
                        field: issue.path.join('.'),
                        message: issue.message,
                        value: issue.code,
                    })),
                }
            );

            res.status(StatusCodes.BAD_REQUEST).json(errorResponse);
        }
    };
}
