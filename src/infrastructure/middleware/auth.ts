/**
 * Authentication Middleware
 * 
 * Handles user authentication and authorization.
 */

import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { createErrorResponse } from '@/shared/utils/response-formatter';
import { Logger } from '@/infrastructure/logger/winston-logger';

/**
 * Extended Request interface with userId
 */
interface AuthenticatedRequest extends Request {
    userId?: string;
}

/**
 * Extract user ID from request headers
 */
export function validateUserId(
    req: Request,
    res: Response,
    next: NextFunction
): void {
    const userId = req.headers['userid'];
    
    if (typeof userId !== 'string' || userId.trim() === '') {
        Logger.warn('Missing or invalid user ID', {
            headers: req.headers,
            ip: req.ip ?? 'unknown',
        });
        
        const errorResponse = createErrorResponse(
            'User ID is required',
            'Please provide a valid user ID in the userId header'
        );
        
        res.status(StatusCodes.UNAUTHORIZED).json(errorResponse);
        return;
    }

    // Add user ID to request object for downstream middleware
    (req as AuthenticatedRequest)['userId'] = userId.trim();
    next();
}

/**
 * Optional authentication middleware
 */
export function optionalAuth(
    req: Request,
    _res: Response,
    next: NextFunction
): void {
    const userId = req.headers['userId'];
    
    if (typeof userId === 'string' && userId.trim() !== '') {
        (req as AuthenticatedRequest)['userId'] = userId.trim();
    }
    
    next();
}

/**
 * Check if user has permission to access resource
 */
export function checkPermission(permission: string) {
    return (req: Request, res: Response, next: NextFunction): void => {
        const authenticatedReq = req as AuthenticatedRequest;
        const userId = authenticatedReq['userId'];
        
        if (userId === undefined || userId === '' || userId.trim() === '') {
            Logger.error('Permission check failed: No user ID', {
                permission,
                path: req.path,
            });
            
            const errorResponse = createErrorResponse(
                'Authentication required',
                'User must be authenticated to access this resource'
            );
            
            res.status(StatusCodes.UNAUTHORIZED).json(errorResponse);
            return;
        }

        // TODO: Implement actual permission checking logic
        // For now, just log and continue
        Logger.info('Permission check passed', {
            userId,
            permission,
            path: req.path,
        });
        
        next();
    };
}
