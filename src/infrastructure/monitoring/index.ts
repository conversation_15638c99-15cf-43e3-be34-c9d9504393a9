/**
 * Monitoring Infrastructure Export
 */

// System Resource Monitor
export class SystemResourceMonitor {
    public getMemoryUsage() {
        const memUsage = process.memoryUsage();
        return {
            used: memUsage.heapUsed,
            total: memUsage.heapTotal,
            percentage: (memUsage.heapUsed / memUsage.heapTotal) * 100,
        };
    }

    public getCpuUsage() {
        const cpuUsage = process.cpuUsage();
        return {
            usage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
        };
    }

    public getUptime() {
        return process.uptime();
    }
} 