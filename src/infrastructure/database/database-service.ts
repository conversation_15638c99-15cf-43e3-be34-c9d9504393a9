/**
 * 数据库服务
 * 
 * 管理数据库连接并提供健康检查功能。
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { getDatabaseConfig } from '@/infrastructure/config';
import { logInfo, logError } from '@/infrastructure/logger';
import type { BaseService, ServiceHealth } from '@/shared/types/common-types';

const databaseConfig = getDatabaseConfig();

class DatabaseService implements BaseService {
    public readonly name = 'database';
    private client: postgres.Sql | null = null;
    private db: ReturnType<typeof drizzle> | null = null;
    private isInitialized = false;

    /**
     * 初始化数据库连接
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        try {
            // 创建 PostgreSQL 客户端
            this.client = postgres({
                host: databaseConfig['host'] as string,
                port: databaseConfig['port'] as number,
                database: databaseConfig['database'] as string,
                username: databaseConfig['user'] as string,
                password: databaseConfig['password'] as string,
                ssl: databaseConfig['ssl'] as boolean,
                max: 10,
                idle_timeout: 20,
                connect_timeout: 10,
            });

            // 创建 Drizzle 实例
            this.db = drizzle(this.client);

            // 测试连接
            await this.client`SELECT 1`;

            this.isInitialized = true;
            logInfo('Database service initialized successfully', {
                host: databaseConfig['host'] as string,
                port: databaseConfig['port'] as number,
                database: databaseConfig['database'] as string,
                ssl: databaseConfig['ssl'] as boolean,
            });

        } catch (error) {
            logError('Failed to initialize database service', {}, error as Error);
            throw error;
        }
    }

    /**
     * 销毁数据库连接
     */
    async destroy(): Promise<void> {
        if (!this.isInitialized || !this.client) {
            return;
        }

        try {
            await this.client.end();
            this.client = null;
            this.db = null;
            this.isInitialized = false;
            logInfo('Database service destroyed successfully');
        } catch (error) {
            logError('Error destroying database service', {}, error as Error);
            throw error;
        }
    }

    /**
     * 数据库服务健康检查
     */
    async healthCheck(): Promise<ServiceHealth> {
        if (!this.isInitialized || !this.client) {
            return {
                status: 'unhealthy',
                error: 'Database service not initialized',
            };
        }

        try {
            const start = Date.now();
            await this.client`SELECT 1`;
            const responseTime = Date.now() - start;

            return {
                status: 'healthy',
                responseTime,
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: (error as Error).message,
            };
        }
    }

    /**
     * 获取数据库客户端
     */
    getClient(): postgres.Sql {
        if (!this.client) {
            throw new Error('Database client not initialized');
        }
        return this.client;
    }

    /**
     * 获取 Drizzle 数据库实例
     */
    getDb(): ReturnType<typeof drizzle> {
        if (!this.db) {
            throw new Error('Database instance not initialized');
        }
        return this.db;
    }
}

// 导出单例实例
export const databaseService = new DatabaseService(); 