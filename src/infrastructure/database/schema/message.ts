/**
 * Message 数据表模式定义
 *
 * 根据A2U (Agent-to-User) 协议规范定义的消息表，用于存储用户和代理之间交换的消息
 * 支持多种内容类型（文本、文件、工具调用、工具结果、数据）
 */
import { pgTable, uuid, varchar, jsonb, timestamp, text } from 'drizzle-orm/pg-core';
import { InferSelectModel } from 'drizzle-orm';
import { session } from './session';

/**
 * Message 数据表
 * 基于A2U协议规范的A2UBaseMessage接口定义
 */
export const message = pgTable('message', {
    /** 数据库内部ID，主键，自动生成UUID */
    dbId: uuid('db_id').primaryKey().notNull().defaultRandom(),

    /** A2U协议中的id，消息的唯一标识符 */
    id: text('id').notNull(),

    /** 消息发送者角色：'user' 用户 或 'assistant' 助手 */
    role: varchar('role', { enum: ['user', 'assistant'] }).notNull(),

    /** 消息内容数组，MessageContent[]，支持text、file、data类型 */
    content: jsonb('content').notNull().$type<
        Array<{
            type: 'text' | 'file' | 'data';
            // text类型
            text?: string;
            // file类型
            file?: {
                bytes?: string; // base64编码的文件内容
                uri?: string; // 文件URI地址
                metadata?: Record<string, unknown>;
            };
            // data类型
            data?: Record<string, unknown> | string;
            metadata?: Record<string, unknown>;
        }>
    >(),
    /** 消息发送者信息（可选），支持A2U协议中的Sender接口 */
    sender: jsonb('sender').$type<{
        /** 发送者的唯一标识符（必需） */
        id: string;
        /** 发送者显示名称（可选） */
        name?: string;
        /** 发送者头像URL（可选） */
        avatar?: string;
        /** 发送者类型（可选） */
        type?: string;
        /** 其他自定义属性 */
        [key: string]: unknown;
    }>(),

    /** 消息扩展数据，用于存储额外的自定义信息 */
    extendedData: jsonb('extended_data').$type<Record<string, unknown>>(),

    /** 消息创建时间戳 */
    createdAt: timestamp('created_at').notNull().defaultNow(),

    /** 消息最后更新时间戳 */
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),

    /** 所属会话ID，外键引用 session 表的 id 字段 */
    sessionId: uuid('session_id')
        .notNull()
        .references(() => session.id, { onDelete: 'cascade' }),

    /** 消息创建用户ID */
    userId: varchar('user_id', { length: 255 }),

    /** 业务标签，用于消息筛选和分类，可以是单个或多个字符串 */
    tags: text('tags').array(),
});

/**
 * Message 类型定义
 * 根据数据表模式自动推断的 TypeScript 类型
 */
export type Message = InferSelectModel<typeof message>;

/**
 * MessageInsert 类型定义
 * 用于插入新消息时的数据类型
 */
export type MessageInsert = typeof message.$inferInsert;

/**
 * A2U MessageContent 类型定义
 * 对应A2U协议中的MessageContent类型
 */
export type MessageContent = {
    type: 'text' | 'file' | 'data';
    // text类型
    text?: string;
    // file类型
    file?: {
        bytes?: string; // base64编码的文件内容
        uri?: string; // 文件URI地址
        metadata?: Record<string, unknown>;
    };
    // data类型
    data?:Record<string, unknown> | string;
    metadata?: Record<string, unknown>;
};

/**
 * 向后兼容的DBMessage类型别名
 * @deprecated 请使用 Message 类型
 */
export type DBMessage = Message;
