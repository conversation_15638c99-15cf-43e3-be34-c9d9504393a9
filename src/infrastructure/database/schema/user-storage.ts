/**
 * User Storage Schema
 * 
 * 通用的用户键值存储表，支持存储任意 JSON 数据
 */

import { pgTable, uuid, varchar, jsonb, timestamp, text } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod/v4';

/**
 * 用户存储表
 * 为每个用户提供通用的键值存储功能
 */
export const userStorage = pgTable('user_storage', {
    /**
     * 主键ID
     */
    id: uuid('id').primaryKey().defaultRandom(),

    /**
     * 用户ID - 关联到具体用户
     */
    userId: text('user_id').notNull(),

    /**
     * 存储键名 - 用于标识存储的数据类型或用途
     * 例如: 'preferences', 'settings', 'cache', 'temp_data' 等
     */
    key: text('key').unique().notNull(),

    /**
     * JSON 值 - 存储任意结构的 JSON 数据
     * 使用 jsonb 类型以获得更好的性能和查询能力
     */
    value: jsonb('value').notNull(),

    /**
     * 可选的描述信息
     */
    description: varchar('description', { length: 500 }),

    /**
     * 数据版本号 - 用于乐观锁或版本控制
     */
    version: varchar('version', { length: 50 }).default('1.0'),

    /**
     * 过期时间 - 可选，用于自动清理过期数据
     */
    expiresAt: timestamp('expires_at', { withTimezone: true }),

    /**
     * 创建时间
     */
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),

    /**
     * 更新时间
     */
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
});

/**
 * 用户存储表类型定义
 */
export type UserStorage = typeof userStorage.$inferSelect;
export type InsertUserStorage = typeof userStorage.$inferInsert;
export type UpdateUserStorage = Partial<Omit<InsertUserStorage, 'id' | 'createdAt'>>;

/**
 * Zod 验证模式
 */
export const insertUserStorageSchema = createInsertSchema(userStorage);

export const selectUserStorageSchema = createSelectSchema(userStorage);

// 手动创建带验证的插入模式
export const insertUserStorageValidationSchema = z.object({
    userId: z.string().min(1, '用户ID不能为空'),
    key: z.string().min(1, '键名不能为空'),
    value: z.unknown(), // JSON 可以是任意类型，使用 unknown 更安全
    description: z.string().max(500, '描述长度不能超过500个字符').optional(),
    version: z.string().max(50, '版本号长度不能超过50个字符').optional(),
    expiresAt: z.date().optional(),
});

export const updateUserStorageSchema = z.object({
    id: z.uuid('ID必须是有效的UUID'),
    userId: z.string().min(1, '用户ID不能为空').optional(),
    key: z.string().min(1, '键名不能为空').optional(),
    value: z.unknown().optional(),
    description: z.string().max(500, '描述长度不能超过500个字符').optional(),
    version: z.string().max(50, '版本号长度不能超过50个字符').optional(),
    expiresAt: z.date().optional(),
});

/**
 * 常用的查询条件类型
 */
export interface UserStorageQuery {
    userId: string;
    key?: string;
    keys?: string[];
    includeExpired?: boolean;
}

/**
 * 批量操作类型
 */
export interface BatchUserStorageOperation {
    userId: string;
    operations: Array<{
        action: 'upsert' | 'delete';
        key: string;
        value?: unknown;
        description?: string;
        version?: string;
        expiresAt?: Date;
    }>;
} 