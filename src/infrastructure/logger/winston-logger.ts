/**
 * <PERSON>日志配置
 * 
 * 使用Winston的集中式日志服务，支持结构化日志。
 */

import winston from 'winston';
import { getLoggingConfig } from '@/infrastructure/config';
import type { LogContext } from '@/shared/types';

/**
 * 日志级别配置
 */
const LOG_LEVELS = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
} as const;

const consoleFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.colorize(),
    winston.format.printf(({ level, message, timestamp, ...meta }) => {
        const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
        return `${timestamp} [${level}]: ${message} ${metaStr}`;
    })
);


/**
 * 生产环境JSON格式
 */
const jsonFormat = winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
);

/**
 * 创建传输器
 */
const createTransports = (): winston.transport[] => {
    const loggingConfig = getLoggingConfig();
    const transports: winston.transport[] = [];

    // 控制台传输器（始终启用）
    transports.push(
        new winston.transports.Console({
            format: loggingConfig['format'] === 'console' ?  consoleFormat: jsonFormat,
        })
    );

    // 文件传输器（始终启用，便于日志存储）
    transports.push(
        new winston.transports.File({
            filename: loggingConfig['errorLogFile'] as string,
            level: 'error',
            format: jsonFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        new winston.transports.File({
            filename: loggingConfig['combinedLogFile'] as string,
            format: jsonFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        })
    );

    return transports;
};

/**
 * 创建Winston日志实例
 */
const createLogger = (): winston.Logger => {
    const loggingConfig = getLoggingConfig();
    return winston.createLogger({
        level: loggingConfig['level'] as string,
        levels: LOG_LEVELS,
        defaultMeta: {
            service: 'xui-app-server',
        },
        transports: createTransports(),
        exitOnError: false,
    });
};

// 延迟初始化的日志实例
let winstonLogger: winston.Logger | null = null;

/**
 * 获取日志实例（延迟初始化）
 */
const getLoggerInstance = (): winston.Logger => {
    winstonLogger ??= createLogger();
    return winstonLogger;
};

/**
 * 增强的日志功能，支持上下文
 */
export class Logger {
    private static get instance(): winston.Logger {
        return getLoggerInstance();
    }

    /**
     * 记录错误消息
     */
    public static error(message: string, context?: LogContext, error?: Error): void {
        Logger.instance.error(message, {
            ...context,
            ...(error && {
                error: {
                    name: error.name,
                    message: error.message,
                    stack: error.stack,
                },
            }),
        });
    }

    /**
     * 记录警告消息
     */
    public static warn(message: string, context?: LogContext): void {
        Logger.instance.warn(message, context);
    }

    /**
     * 记录信息消息
     */
    public static info(message: string, context?: LogContext): void {
        Logger.instance.info(message, context);
    }

    /**
     * 记录HTTP请求
     */
    public static http(message: string, context?: LogContext): void {
        Logger.instance.http(message, context);
    }

    /**
     * 记录调试消息
     */
    public static debug(message: string, context?: LogContext): void {
        Logger.instance.debug(message, context);
    }

    /**
     * 获取日志实例
     */
    public static getInstance(): winston.Logger {
        return Logger.instance;
    }

    /**
     * 创建带默认上下文的子日志器
     */
    public static child(defaultContext: LogContext): winston.Logger {
        return Logger.instance.child(defaultContext);
    }
}

// 导出便利函数
export const logError = Logger.error.bind(Logger);
export const logWarn = Logger.warn.bind(Logger);
export const logInfo = Logger.info.bind(Logger);
export const logHttp = Logger.http.bind(Logger);
export const logDebug = Logger.debug.bind(Logger);

/**
 * 数据库操作日志记录器
 */
export const logDatabaseOperation = (
    operation: string, 
    table: string, 
    duration?: number, 
    context?: LogContext
): void => {
    const durationText = (duration !== undefined && duration > 0) ? ` (${duration}ms)` : '';
    Logger.info(`Database operation: ${operation} on ${table}${durationText}`, {
        ...context,
        operation,
        table,
        ...(duration !== undefined && duration > 0 && { duration }),
    });
};

/**
 * Morgan HTTP日志记录流
 */
export const logStream = {
    write: (message: string): void => {
        Logger.http(message.trim());
    },
};

// 注意：为避免循环依赖，建议直接使用 Logger 类的静态方法
// 例如：Logger.info(), Logger.error() 等
// 如需原始 winston 实例，请调用 Logger.getInstance() 