/**
 * Langfuse集成服务
 * 
 * 提供Langfuse客户端初始化和配置。
 */

import { Langfuse } from 'langfuse';
import { Logger } from './winston-logger';
import type { 
    JsonValue, 
    LangfuseTrace, 
    LangfuseTraceMetadata,
    LangfuseGenerationConfig,
    LangfuseScoreConfig
} from '@/shared/types';

/**
 * Langfuse服务类
 */
class LangfuseService {
    private client: Langfuse | null = null;
    private readonly enabled: boolean;

    constructor() {
        // 延迟初始化配置检查
        const config = this.getLangfuseConfig();
        this.enabled = config['enabled'] as boolean;
        this.initialize();
    }

    /**
     * 获取Langfuse配置
     */
    private getLangfuseConfig(): Record<string, unknown> {
        return {
            publicKey: process.env['LANGFUSE_PUBLIC_KEY'],
            secretKey: process.env['LANGFUSE_SECRET_KEY'],
            baseUrl: process.env['LANGFUSE_HOST'] ?? 'https://langfuse.yxt.com.cn',
            enabled: !!(
                process.env['LANGFUSE_PUBLIC_KEY'] !== undefined 
                && process.env['LANGFUSE_PUBLIC_KEY'].trim() !== ''
                && process.env['LANGFUSE_SECRET_KEY'] !== undefined
                && process.env['LANGFUSE_SECRET_KEY'].trim() !== ''
            ),
        };
    }

    /**
     * 初始化Langfuse客户端
     */
    private initialize(): void {
        if (!this.enabled) {
            Logger.info('Langfuse client not configured');
            return;
        }

        try {
            const config = this.getLangfuseConfig();
            this.client = new Langfuse({
                publicKey: config['publicKey'] as string,
                secretKey: config['secretKey'] as string,
                baseUrl: config['baseUrl'] as string,
            });

            Logger.info('Langfuse client initialized successfully');
        } catch (error) {
            Logger.error('Failed to initialize Langfuse client', {}, error as Error);
        }
    }

    /**
     * 检查Langfuse是否启用并配置
     */
    public isEnabled(): boolean {
        return this.enabled && this.client !== null;
    }

    /**
     * 创建新的追踪
     */
    public createTrace(
        name: string,
        input?: JsonValue,
        metadata?: LangfuseTraceMetadata
    ): LangfuseTrace | null {
        if (!this.isEnabled() || !this.client) {
            return null;
        }

        try {
            return this.client.trace({
                name,
                input: input as unknown,
                metadata: metadata as unknown,
            }) as LangfuseTrace;
        } catch (error) {
            Logger.error('Failed to create Langfuse trace', { name }, error as Error);
            return null;
        }
    }

    /**
     * 创建事件
     */
    public createEvent(
        name: string,
        input?: JsonValue,
        output?: JsonValue,
        metadata?: LangfuseTraceMetadata
    ): void {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            this.client.event({
                name,
                input: input as unknown,
                output: output as unknown,
                metadata: metadata as unknown,
            });
        } catch (error) {
            Logger.error('Failed to create Langfuse event', { name }, error as Error);
        }
    }

    /**
     * 创建生成
     */
    public createGeneration(config: LangfuseGenerationConfig): void {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            this.client.generation(config);
        } catch (error) {
            Logger.error('Failed to create Langfuse generation', { name: config.name }, error as Error);
        }
    }

    /**
     * 为追踪评分
     */
    public scoreTrace(config: LangfuseScoreConfig): void {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            this.client.score(config);
        } catch (error) {
            Logger.error('Failed to score Langfuse trace', { traceId: config.traceId }, error as Error);
        }
    }

    /**
     * 刷新待处理事件
     */
    public async flush(): Promise<void> {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            await this.client.flushAsync();
        } catch (error) {
            Logger.error('Failed to flush Langfuse events', {}, error as Error);
        }
    }

    /**
     * 关闭Langfuse客户端
     */
    public async shutdown(): Promise<void> {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            await this.client.shutdownAsync();
            Logger.info('Langfuse client shutdown successfully');
        } catch (error) {
            Logger.error('Failed to shutdown Langfuse client', {}, error as Error);
        }
    }

    /**
     * 获取客户端实例
     */
    public getClient(): Langfuse | null {
        return this.client;
    }
}

// 延迟初始化的单例实例
let langfuseInstance: LangfuseService | null = null;

/**
 * 获取 Langfuse 服务实例（延迟初始化）
 */
export function getLangfuseService(): LangfuseService {
    langfuseInstance ??= new LangfuseService();
    return langfuseInstance;
}

// 为避免循环依赖，请使用 getLangfuseService() 函数获取实例
export default getLangfuseService; 