/**
 * Agent Routes
 * 
 * RESTful API routes for agent management using modern modular architecture.
 */

import { Router, type IRouter } from 'express';
import { validateUserId } from '@/infrastructure/middleware/auth';
import { container } from 'tsyringe';
import { TYPES } from '@/shared/constants';
import type { AgentController } from '@/modules/agent/controllers';
import {
    validateCreateAgent,
    validateUpdateAgent,
    validateGetAgent,
    validateDeleteAgent,
    validateListAgents,
} from '@/modules/agent/validators';

const router: IRouter = Router({ strict: true });

// Initialize controller
const agentController = container.resolve<AgentController>(TYPES.AgentController);

/**
 * @openapi
 * /api/agent:
 *   get:
 *     tags:
 *       - Agent
 *     summary: 获取 Agent 列表
 *     description: 获取当前用户的 Agent 列表，支持分页
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *     responses:
 *       '200':
 *         description: 成功获取 Agent 列表
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ListAgentsResponse'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '500':
 *         $ref: '#/components/responses/InternalServerError'
 */
router.get(
    '/',
    validateUserId,
    ...validateListAgents,
    agentController.listAgents
);

/**
 * @openapi
 * /api/agent/{agentId}:
 *   get:
 *     tags:
 *       - Agent
 *     summary: 获取指定的 Agent
 *     description: 根据 Agent ID 获取详细信息
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: agentId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要获取的 Agent 的 ID
 *     responses:
 *       '200':
 *         description: 成功获取 Agent
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponse'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 *       '500':
 *         $ref: '#/components/responses/InternalServerError'
 */
router.get(
    '/:agentId',
    validateUserId,
    ...validateGetAgent,
    agentController.getAgentById
);

/**
 * @openapi
 * /api/agent:
 *   post:
 *     tags:
 *       - Agent
 *     summary: 创建一个新的 Agent
 *     description: 为当前用户创建一个新的 Agent
 *     security:
 *       - userIdHeader: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateAgentRequest'
 *     responses:
 *       '201':
 *         description: Agent 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponse'
 *       '400':
 *         $ref: '#/components/responses/BadRequestError'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '500':
 *         $ref: '#/components/responses/InternalServerError'
 */
router.post(
    '/',
    validateUserId,
    ...validateCreateAgent,
    agentController.createAgent
);

/**
 * @openapi
 * /api/agent/{agentId}:
 *   put:
 *     tags:
 *       - Agent
 *     summary: 更新一个 Agent
 *     description: 更新指定 ID 的 Agent 信息
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: agentId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要更新的 Agent 的 ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateAgentRequest'
 *     responses:
 *       '200':
 *         description: Agent 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponse'
 *       '400':
 *         $ref: '#/components/responses/BadRequestError'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 *       '500':
 *         $ref: '#/components/responses/InternalServerError'
 */
router.put(
    '/:agentId',
    validateUserId,
    ...validateUpdateAgent,
    agentController.updateAgent
);

/**
 * @openapi
 * /api/agent/{agentId}:
 *   delete:
 *     tags:
 *       - Agent
 *     summary: 删除一个 Agent
 *     description: 删除指定 ID 的 Agent
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: agentId
 *         required: true
 *         schema:
 *           type: string
 *           format: 'uuid'
 *         description: 要删除的 Agent 的 ID
 *     responses:
 *       '200':
 *         description: Agent 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Agent deleted successfully
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 *       '500':
 *         $ref: '#/components/responses/InternalServerError'
 */
router.delete(
    '/:agentId',
    validateUserId,
    ...validateDeleteAgent,
    agentController.deleteAgent
);

export default router;
