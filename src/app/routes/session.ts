/**
 * Session Routes
 *
 * Defines the routes for session-related operations.
 */
import { Router, type IRouter } from 'express';
import { container } from 'tsyringe';
import { TYPES } from '@/shared/constants';
import type { SessionController, ChatController } from '@/modules/session/controllers';
import {
    validateCreateSession,
    validateGetUserSessions,
    validateGetSession,
    validateDeleteSession,
    validateChat,
    validateUpdateSession,
} from '@/modules/session/validators';
import { validateUserId } from '@/infrastructure/middleware/auth';

const router: IRouter = Router({ strict: true });
const sessionController = container.resolve<SessionController>(TYPES.SessionController);
const chatController = container.resolve<ChatController>(TYPES.ChatController);

/**
 * @openapi
 * tags:
 *   name: Session
 *   description: 会话管理
 */

/**
 * @openapi
 * /api/session:
 *   get:
 *     tags:
 *      - Session
 *     summary: 获取用户会话列表
 *     description: 获取当前用户的所有会话，支持分页
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: agentGroup
 *         schema:
 *           type: integer
 *           enum: [1, 2, 3]
 *         description: Agent 分组
 *     responses:
 *       '200':
 *         description: 成功获取会话列表
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ListSessionsResponse'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 */
router.get(
    '/',
    validateUserId,
    validateGetUserSessions,
    sessionController.getUserSessions
);

/**
 * @openapi
 * /api/session/create:
 *   get:
 *     tags:
 *      - Session
 *     summary: 创建新会话
 *     description: 为指定的 Agent 创建一个新的会话
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: query
 *         name: agentId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要创建会话的 Agent ID
 *     responses:
 *       '201':
 *         description: 成功创建会话
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Session'
 *       '400':
 *         $ref: '#/components/responses/BadRequestError'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 */
router.get(
    '/create',
    validateUserId,
    validateCreateSession,
    sessionController.createSession
);

/**
 * @openapi
 * /api/session/{sessionId}:
 *   get:
 *     tags:
 *      - Session
 *     summary: 获取会话详情
 *     description: 获取指定 ID 的会话详细信息
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 会话 ID
 *     responses:
 *       '200':
 *         description: 成功获取会话
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SessionWithAgent'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.get(
    '/:sessionId',
    validateUserId,
    validateGetSession,
    sessionController.getSessionById
);

/**
 * @openapi
 * /api/session/{sessionId}:
 *   delete:
 *     tags:
 *      - Session
 *     summary: 删除会话
 *     description: 删除指定 ID 的会话
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 会话 ID
 *     responses:
 *       '200':
 *         description: 会话删除成功
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.delete(
    '/:sessionId',
    validateUserId,
    validateDeleteSession,
    sessionController.deleteSession
);

/**
 * @openapi
 * /api/session/{sessionId}:
 *   post:
 *     tags:
 *      - Session
 *     summary: 发送聊天消息
 *     description: 向指定会话发送消息，并获取流式响应
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 会话 ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ChatRequest'
 *     responses:
 *       '200':
 *         description: 成功获取流式响应
 *         content:
 *           text/event-stream:
 *             schema:
 *               type: string
 *       '400':
 *         $ref: '#/components/responses/BadRequestError'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.post(
    '/:sessionId',
    validateUserId,
    validateChat,
    chatController.chat
)

/**
 * @openapi
 * /api/session/{sessionId}:
 *   put:
 *     tags:
 *      - Session
 *     summary: 更新会话
 *     description: 更新指定 ID 的会话信息，例如标题
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 会话 ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateSessionRequest'
 *     responses:
 *       '200':
 *         description: 成功更新会话
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Session'
 *       '400':
 *         $ref: '#/components/responses/BadRequestError'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.put(
    '/:sessionId',
    validateUserId,
    validateUpdateSession,
    sessionController.updateSession
);

export default router;
