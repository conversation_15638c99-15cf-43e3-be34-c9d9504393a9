/**
 * User Storage Routes
 * 
 * Defines the routes for user storage operations.
 */

import { Router, type IRouter } from 'express';
import { container } from 'tsyringe';
import { TYPES } from '@/shared/constants';
import { validateUserId } from '@/infrastructure/middleware/auth';
import type { UserStorageController } from '@/modules/user-storage/controllers/user-storage-controller';
import {
    validateUserStorageKey,
    validateCreateUserStorageRequest,
    validateUpdateUserStorageRequest,
} from '@/modules/user-storage/validators';

const router: IRouter = Router({ strict: true });
const userStorageController = container.resolve<UserStorageController>(TYPES.UserStorageController);

/**
 * @openapi
 * tags:
 *   name: User Storage
 *   description: 用户存储管理
 */

/**
 * @openapi
 * /api/user-storage/{key}:
 *   get:
 *     tags:
 *      - User Storage
 *     summary: 获取用户存储数据
 *     description: 根据键名获取用户存储的数据
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: 存储键名
 *     responses:
 *       '200':
 *         description: 成功获取用户存储数据
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserStorageResponse'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.get(
    '/:key',
    validateUserId,
    validateUserStorageKey,
    userStorageController.getByKey
);

/**
 * @openapi
 * /api/user-storage/{key}:
 *   post:
 *     tags:
 *      - User Storage
 *     summary: 创建用户存储数据
 *     description: 根据键名创建用户存储数据
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: 存储键名
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateUserStorageRequest'
 *     responses:
 *       '200':
 *         description: 成功创建用户存储数据
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserStorageResponse'
 *       '400':
 *         $ref: '#/components/responses/BadRequestError'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 */
router.post(
    '/:key',
    validateUserId,
    validateUserStorageKey,
    validateCreateUserStorageRequest,
    userStorageController.createByKey
);

/**
 * @openapi
 * /api/user-storage/{key}:
 *   put:
 *     tags:
 *      - User Storage
 *     summary: 更新用户存储数据
 *     description: 根据键名部分更新用户存储数据
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: 存储键名
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateUserStorageRequest'
 *     responses:
 *       '200':
 *         description: 成功更新用户存储数据
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserStorageResponse'
 *       '400':
 *         $ref: '#/components/responses/BadRequestError'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.put(
    '/:key',
    validateUserId,
    validateUserStorageKey,
    validateUpdateUserStorageRequest,
    userStorageController.updateByKey
);

/**
 * @openapi
 * /api/user-storage/{key}:
 *   delete:
 *     tags:
 *      - User Storage
 *     summary: 删除用户存储数据
 *     description: 根据键名删除用户存储数据
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: 存储键名
 *     responses:
 *       '200':
 *         description: 用户存储数据删除成功
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.delete(
    '/:key',
    validateUserId,
    validateUserStorageKey,
    userStorageController.deleteByKey
);

export default router; 