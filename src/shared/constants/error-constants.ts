/**
 * Error Constants
 * 
 * Contains all error messages and codes used throughout the application.
 */

export const ERROR_MESSAGES = {
    // Authentication errors
    UNAUTHORIZED: 'Unauthorized access',
    INVALID_TOKEN: 'Invalid or expired token',
    MISSING_AUTH_HEADER: 'Missing authorization header',
    
    // Validation errors
    INVALID_INPUT: 'Invalid input data',
    REQUIRED_FIELD_MISSING: 'Required field is missing',
    INVALID_EMAIL_FORMAT: 'Invalid email format',
    PASSWORD_TOO_WEAK: 'Password does not meet security requirements',
    
    // Resource errors
    RESOURCE_NOT_FOUND: 'Resource not found',
    RESOURCE_ALREADY_EXISTS: 'Resource already exists',
    RESOURCE_LIMIT_EXCEEDED: 'Resource limit exceeded',
    
    // Database errors
    DATABASE_CONNECTION_ERROR: 'Database connection error',
    DATABASE_QUERY_ERROR: 'Database query error',
    DATABASE_TRANSACTION_ERROR: 'Database transaction error',
    
    // Server errors
    INTERNAL_SERVER_ERROR: 'Internal server error',
    SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
    REQUEST_TIMEOUT: 'Request timeout',
    RATE_LIMIT_EXCEEDED: 'Rate limit exceeded',
    
    // Session errors
    SESSION_NOT_FOUND: 'Session not found',
    SESSION_EXPIRED: 'Session expired',
    SESSION_ACCESS_DENIED: 'Session access denied',
    
    // Agent errors
    AGENT_NOT_FOUND: 'Agent not found',
    AGENT_UNAVAILABLE: 'Agent is not available',
    AGENT_CONFIGURATION_ERROR: 'Agent configuration error',
    
    // Message errors
    MESSAGE_NOT_FOUND: 'Message not found',
    MESSAGE_TOO_LONG: 'Message content too long',
    INVALID_MESSAGE_FORMAT: 'Invalid message format',
} as const;

export const ERROR_CODES = {
    // HTTP Status Codes
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_ALLOWED: 405,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    TOO_MANY_REQUESTS: 429,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503,
    
    // Custom Application Codes
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    AUTHENTICATION_ERROR: 'AUTH_ERROR',
    AUTHORIZATION_ERROR: 'AUTHZ_ERROR',
    RESOURCE_ERROR: 'RESOURCE_ERROR',
    DATABASE_ERROR: 'DB_ERROR',
    EXTERNAL_SERVICE_ERROR: 'EXT_SERVICE_ERROR',
} as const; 