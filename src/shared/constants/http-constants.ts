/**
 * HTTP Constants
 * 
 * Contains all HTTP-related constants including headers, and CORS settings.
 */

/**
 * HTTP Methods
 */
export const HTTP_METHODS = {
    GET: 'GET',
    POST: 'POST',
    PUT: 'PUT',
    DELETE: 'DELETE',
    PATCH: 'PATCH',
    HEAD: 'HEAD',
    OPTIONS: 'OPTIONS',
} as const;

/**
 * Content Types
 */
export const CONTENT_TYPES = {
    JSON: 'application/json',
    TEXT: 'text/plain',
    HTML: 'text/html',
    XML: 'application/xml',
    FORM_URLENCODED: 'application/x-www-form-urlencoded',
    MULTIPART_FORM_DATA: 'multipart/form-data',
    OCTET_STREAM: 'application/octet-stream',
} as const;

/**
 * Standard HTTP Headers
 */
export const HTTP_HEADERS = {
    AUTHORIZATION: 'Authorization',
    CONTENT_TYPE: 'Content-Type',
    CONTENT_LENGTH: 'Content-Length',
    ACCEPT: 'Accept',
    USER_AGENT: 'User-Agent',
    CACHE_CONTROL: 'Cache-Control',
    X_REQUEST_ID: 'X-Request-ID',
    X_FORWARDED_FOR: 'X-Forwarded-For',
    X_REAL_IP: 'X-Real-IP',
    
    // CORS Headers
    CORS: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Request-ID',
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Max-Age': '86400',
    },
    SECURITY: {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
    },
} as const;

/**
 * Cache Control Values
 */
export const CACHE_CONTROL = {
    NO_CACHE: 'no-cache',
    NO_STORE: 'no-store',
    MUST_REVALIDATE: 'must-revalidate',
    PUBLIC: 'public',
    PRIVATE: 'private',
    MAX_AGE_ONE_HOUR: 'max-age=3600',
    MAX_AGE_ONE_DAY: 'max-age=86400',
    MAX_AGE_ONE_WEEK: 'max-age=604800',
} as const;

export const DEFAULT_PAGINATION = {
    PAGE: 1,
    LIMIT: 10,
    MAX_LIMIT: 100,
} as const;

export const REQUEST_LIMITS = {
    JSON_BODY_SIZE: '10mb',
    URL_ENCODED_SIZE: '10mb',
    FILE_UPLOAD_SIZE: '50mb',
} as const; 