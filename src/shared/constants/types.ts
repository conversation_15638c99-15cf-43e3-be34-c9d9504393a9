/**
 * Dependency Injection Types
 * 
 * Constants for service identifiers used in TSyringe container.
 */

export const TYPES = {
    // Infrastructure
    DatabaseService: 'DatabaseService',
    LangfuseService: 'LangfuseService',
    Logger: 'Logger',
    NacosConfigService: 'NacosConfigService',
    
    // Repositories
    AgentRepository: 'AgentRepository',
    MessageRepository: 'MessageRepository',
    SessionRepository: 'SessionRepository',
    UserStorageRepository: 'UserStorageRepository',
    
    // Services
    AgentService: 'AgentService',
    MessageService: 'MessageService',
    SessionService: 'SessionService',
    HealthService: 'HealthService',
    ChatService: 'ChatService',
    UserStorageService: 'UserStorageService',
    
    // Mappers
    AgentMapper: 'AgentMapper',
    MessageMapper: 'MessageMapper',
    SessionMapper: 'SessionMapper',
    UserStorageMapper: 'UserStorageMapper',
    
    // Controllers
    AgentController: 'AgentController',
    MessageController: 'MessageController',
    SessionController: 'SessionController',
    HealthController: 'HealthController',
    ChatController: 'ChatController',
    UserStorageController: 'UserStorageController',
} as const; 