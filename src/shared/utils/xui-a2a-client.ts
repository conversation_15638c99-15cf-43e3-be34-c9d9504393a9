/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { A2AClient, MessageSendParams } from '@a2a-js/sdk';

export class XuiA2AClient extends A2AClient {
    private customHeaders: Record<string, string> = {};

    constructor(agentBaseUrl: string) {
        super(agentBaseUrl);
    }

    public setHeaders(headers: Record<string, string>): void {
        this.customHeaders = { ...this.customHeaders, ...headers };
    }

    public getHeaders(): Record<string, string> {
        return { ...this.customHeaders };
    }

    public setHeader(key: string, value: string): void {
        this.customHeaders[key] = value;
    }

    /**
     * 重写 sendMessageStream 方法以支持自定义请求头
     */
    public override async *sendMessageStream(params: MessageSendParams): AsyncGenerator<any, void, undefined> {
        const agentCard = await this.getAgentCard();

        if (!(agentCard.capabilities as { streaming?: boolean })?.streaming) {
            throw new Error("Agent does not support streaming (AgentCard.capabilities.streaming is not true).");
        }

        const endpoint = await (this as any)._getServiceEndpoint();
        const clientRequestId = (this as any).requestIdCounter++;
        const rpcRequest = {
            jsonrpc: "2.0",
            method: "message/stream",
            params,
            id: clientRequestId,
        };

        const headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream",
            ...this.customHeaders,
        };

        const response = await global.fetch(endpoint, {
            method: "POST",
            headers,
            body: JSON.stringify(rpcRequest),
        });

        if (!response.ok) {
            let errorBody = "";
            try {
                errorBody = await response.text();
                const errorJson = JSON.parse(errorBody) as { error?: { message: string; code: string } };

                if (errorJson.error) {
                    throw new Error(
                        `HTTP error establishing stream for message/stream: ${response.status} ${response.statusText}. RPC Error: ${errorJson.error.message} (Code: ${errorJson.error.code})`
                    );
                }
            } catch (e) {
                if (e instanceof Error && e.message.startsWith('HTTP error establishing stream')) {
                    throw e;
                }
                throw new Error(`HTTP error establishing stream for message/stream: ${response.status} ${response.statusText}. Response: ${errorBody || '(empty)'}`);
            }
            throw new Error(`HTTP error establishing stream for message/stream: ${response.status} ${response.statusText}`);
        }

        if (!response.headers.get("Content-Type")?.startsWith("text/event-stream")) {
            throw new Error("Invalid response Content-Type for SSE stream. Expected 'text/event-stream'.");
        }

        yield* (this as any)._parseA2ASseStream(response, clientRequestId);
    }
} 