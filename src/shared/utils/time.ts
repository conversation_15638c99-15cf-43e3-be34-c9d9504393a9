/**
 * 时间工具
 * 
 * 提供时间测量、格式化和超时处理功能。
 */

import { performance } from 'perf_hooks';
import { Logger } from '@/infrastructure/logger';

/**
 * Sleep for specified milliseconds
 */
export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Get current timestamp in milliseconds
 */
export function now(): number {
    return Date.now();
}

/**
 * Get current timestamp in seconds
 */
export function nowInSeconds(): number {
    return Math.floor(Date.now() / 1000);
}

/**
 * Format duration in milliseconds to human-readable string
 */
export function formatDuration(ms: number): string {
    if (ms < 1000) {
        return `${Math.round(ms)}ms`;
    }
    if (ms < 60000) {
        return `${(ms / 1000).toFixed(2)}s`;
    }
    if (ms < 3600000) {
        return `${(ms / 60000).toFixed(2)}m`;
    }
    return `${(ms / 3600000).toFixed(2)}h`;
}

/**
 * Measure execution time of a function
 */
export async function measureTime<T>(
    operation: () => Promise<T> | T,
    label?: string
): Promise<{ result: T; duration: number }> {
    const start = performance.now();
    
    try {
        const result = await operation();
        const duration = performance.now() - start;
        
        if (typeof label === 'string' && label.length > 0) {
            Logger.debug(`${label} took ${formatDuration(duration)}`);
        }
        
        return { result, duration };
    } catch (e: unknown) {
        const duration = performance.now() - start;
        const error = e instanceof Error ? e : new Error(String(e));
        
        if (typeof label === 'string' && label.length > 0) {
            Logger.error(`${label} failed after ${formatDuration(duration)}`, {}, error);
        }
        
        throw error;
    }
}

/**
 * Measure execution time synchronously
 */
export function measureTimeSync<T>(
    operation: () => T,
    label?: string
): { result: T; duration: number } {
    const start = performance.now();
    
    try {
        const result = operation();
        const duration = performance.now() - start;
        
        if (typeof label === 'string' && label.length > 0) {
            Logger.debug(`${label} took ${formatDuration(duration)}`);
        }
        
        return { result, duration };
    } catch (e: unknown) {
        const duration = performance.now() - start;
        const error = e instanceof Error ? e : new Error(String(e));
        
        if (typeof label === 'string' && label.length > 0) {
            Logger.error(`${label} failed after ${formatDuration(duration)}`, {}, error);
        }
        
        throw error;
    }
}

/**
 * Create a timeout promise
 */
export function timeout<T>(promise: Promise<T>, ms: number): Promise<T> {
    return Promise.race([
        promise,
        new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error(`Operation timed out after ${ms}ms`)), ms)
        ),
    ]);
}

/**
 * Retry an operation with exponential backoff
 */
export async function retry<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000,
    maxDelay: number = 10000
): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await operation();
        } catch (error) {
            lastError = error instanceof Error ? error : new Error(String(error));
            
            if (attempt === maxAttempts) {
                throw lastError;
            }
            
            const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
            await sleep(delay);
        }
    }
    
    throw new Error('Retry operation failed after all attempts');
}

/**
 * Parse duration string to milliseconds
 */
export function parseDuration(duration: string): number {
    const units = {
        ms: 1,
        s: 1000,
        m: 60000,
        h: 3600000,
        d: 86400000,
    } as const;
    
    type TimeUnit = keyof typeof units;
    
    const match = duration.match(/^(\d+(?:\.\d+)?)(ms|s|m|h|d)$/);
    if (!match) {
        throw new Error(`Invalid duration format: ${duration}`);
    }
    
    const value = match[1];
    const unit = match[2];
    
    if (typeof value !== 'string' || typeof unit !== 'string' || !(unit in units)) {
        throw new Error(`Invalid duration format: ${duration}`);
    }
    
    return parseFloat(value) * units[unit as TimeUnit];
}

/**
 * Get relative time string
 */
export function getRelativeTime(date: Date | number): string {
    const now = Date.now();
    const then = typeof date === 'number' ? date : date.getTime();
    const diff = now - then;
    
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
        return `${days} day${days === 1 ? '' : 's'} ago`;
    }
    if (hours > 0) {
        return `${hours} hour${hours === 1 ? '' : 's'} ago`;
    }
    if (minutes > 0) {
        return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
    }
    if (seconds > 0) {
        return `${seconds} second${seconds === 1 ? '' : 's'} ago`;
    }
    return 'just now';
}

/**
 * Check if a date is within a time range
 */
export function isWithinTimeRange(
    date: Date | number,
    rangeStart: Date | number,
    rangeEnd: Date | number
): boolean {
    const timestamp = typeof date === 'number' ? date : date.getTime();
    const start = typeof rangeStart === 'number' ? rangeStart : rangeStart.getTime();
    const end = typeof rangeEnd === 'number' ? rangeEnd : rangeEnd.getTime();
    
    return timestamp >= start && timestamp <= end;
}

/**
 * Get formatted date-time string
 */
export function getDateTime(date: Date = new Date()): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

export async function withTimeout<T>(
    promise: Promise<T>, 
    timeout: number, 
    label?: string
): Promise<T> {
    const taskLabel = label ?? 'Task';

    return new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
            const error = new Error(`${taskLabel} timed out after ${timeout}ms`);
            Logger.error(error.message, {}, error);
            reject(error);
        }, timeout);

        promise
            .then(resolve)
            .catch(reject)
            .finally(() => {
                clearTimeout(timer);
            });
    });
}

export function measureSync<T>(label: string, fn: () => T): T {
    const startTime = Date.now();
    try {
        const result = fn();
        const duration = Date.now() - startTime;
        Logger.info(`${label} finished in ${formatDuration(duration)}`);
        return result;
    } catch (e: unknown) {
        const duration = Date.now() - startTime;
        const error = e instanceof Error ? e : new Error(String(e));
        Logger.error(`${label} failed after ${formatDuration(duration)}`, {}, error);
        throw error;
    }
}

export async function measureAsync<T>(label: string, fn: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    try {
        const result = await fn();
        const duration = Date.now() - startTime;
        Logger.info(`${label} finished in ${formatDuration(duration)}`);
        return result;
    } catch (e: unknown) {
        const duration = Date.now() - startTime;
        const error = e instanceof Error ? e : new Error(String(e));
        Logger.error(`${label} failed after ${formatDuration(duration)}`, {}, error);
        throw error;
    }
}

export function withTiming<T>(label: string, fn: () => T): T {
    const startTime = performance.now();
    try {
        return fn();
    } finally {
        const duration = performance.now() - startTime;
        if (typeof label === 'string' && label.length > 0) {
            Logger.debug(`${label} took ${formatDuration(duration)}`);
        }
    }
}

export async function withAsyncTiming<T>(label: string, fn: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    try {
        return await fn();
    } catch (e: unknown) {
        const duration = performance.now() - startTime;
        const error = e instanceof Error ? e : new Error(String(e));
        if (typeof label === 'string' && label.length > 0) {
            Logger.error(`${label} failed after ${formatDuration(duration)}`, {}, error);
        }
        throw error;
    } finally {
        const duration = performance.now() - startTime;
        if (typeof label === 'string' && label.length > 0) {
            Logger.debug(`${label} took ${formatDuration(duration)}`);
        }
    }
}