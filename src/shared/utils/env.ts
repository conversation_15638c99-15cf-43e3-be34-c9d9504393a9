/**
 * 环境配置
 * 
 * 统一配置管理，不区分开发环境和生产环境，支持Nacos配置中心。
 */

import type { Environment, NacosConfig, INacosConfigService } from '@/shared/types';
import { config as loadEnv } from 'dotenv';
import { Logger } from '@/infrastructure/logger';

// 加载本地环境变量文件
loadEnv({ path: '.env' });

/**
 * 解析和验证环境变量
 */
function parseEnvironmentVariables(): Environment {
    const requiredEnvVars = [
        'PORT',
        'HOST',
        'DB_HOST',
        'DB_PORT',
        'DB_NAME',
        'DB_USER',
        'DB_PASSWORD',
    ];

    // 检查必需的环境变量
    const missingEnvVars = requiredEnvVars.filter(varName => {
        const value = process.env[varName];
        return (value ?? '').trim() === '';
    });

    if (missingEnvVars.length > 0) {
        const errorMessage = `Missing required environment variables: ${missingEnvVars.join(', ')}`;
        throw new Error(errorMessage);
    }

    return {
        PORT: parseInt(process.env['PORT'] ?? '3000', 10),
        HOST: process.env['HOST'] ?? 'localhost',
        DB_HOST: process.env['DB_HOST']!,
        DB_PORT: parseInt(process.env['DB_PORT'] ?? '5432', 10),
        DB_NAME: process.env['DB_NAME']!,
        DB_USER: process.env['DB_USER']!,
        DB_PASSWORD: process.env['DB_PASSWORD']!,
        DB_SSL: process.env['DB_SSL'] === 'true',
        BCRYPT_ROUNDS: parseInt(process.env['BCRYPT_ROUNDS'] ?? '10', 10),
        RATE_LIMIT_WINDOW_MS: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] ?? '900000', 10),
        RATE_LIMIT_MAX_REQUESTS: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] ?? '100', 10),
        CORS_ORIGIN: process.env['CORS_ORIGIN'] ?? '*',
        CORS_CREDENTIALS: process.env['CORS_CREDENTIALS'] === 'false' ? false : true,
        LOG_DIR: process.env['LOG_DIR'] ?? 'logs/',
        LOG_LEVEL: process.env['LOG_LEVEL'] ?? 'info',
        LOG_FORMAT: process.env['LOG_FORMAT'] ?? 'json',
        GRACEFUL_SHUTDOWN_TIMEOUT: parseInt(process.env['GRACEFUL_SHUTDOWN_TIMEOUT'] ?? '10000', 10),
        ...(
            process.env['REDIS_URL'] !== undefined
            && process.env['REDIS_URL'].trim() !== ''
            && { REDIS_URL: process.env['REDIS_URL'] }
        ),
        ...(
            process.env['REDIS_PASSWORD'] !== undefined
            && process.env['REDIS_PASSWORD'].trim() !== ''
            && { REDIS_PASSWORD: process.env['REDIS_PASSWORD'] }
        ),
        ENABLE_METRICS: process.env['ENABLE_METRICS'] === 'true',
        METRICS_PORT: parseInt(process.env['METRICS_PORT'] ?? '9090', 10),
        // Nacos配置
        NACOS_ENABLED: process.env['NACOS_ENABLED'] === 'true',
        NACOS_SERVER_ADDR: process.env['NACOS_SERVER_ADDR'] ?? '',
        NACOS_NAMESPACE: process.env['NACOS_NAMESPACE'],
        NACOS_USERNAME: process.env['NACOS_USERNAME'],
        NACOS_PASSWORD: process.env['NACOS_PASSWORD'],
        NACOS_DATA_ID: process.env['NACOS_DATA_ID'] ?? 'app-config',
        NACOS_GROUP: process.env['NACOS_GROUP'] ?? 'DEFAULT_GROUP',
    };
}

/**
 * Nacos配置服务实例
 */
let nacosConfigService: INacosConfigService | null = null;

// 缓存的环境配置，在模块加载时初始化
let cachedEnv: Environment = parseEnvironmentVariables();

/**
 * 获取环境配置（支持Nacos配置覆盖）
 */
export function getEnvironment(): Environment {
    return cachedEnv;
}

/**
 * 重新解析环境配置（在Nacos配置加载后调用）
 */
export function refreshEnvironment(): Environment {
    cachedEnv = parseEnvironmentVariables();
    return cachedEnv;
}

/**
 * 向后兼容的环境配置导出
 * @deprecated 建议使用 getEnvironment() 函数以获得Nacos配置支持
 */
export const env = getEnvironment();

/**
 * 获取Nacos配置
 */
export function getNacosConfig(): NacosConfig | null {
    const env = getEnvironment();
    if (!env.NACOS_ENABLED) {
        return null;
    }

    return {
        enabled: env.NACOS_ENABLED,
        serverAddr: env.NACOS_SERVER_ADDR,
        namespace: env.NACOS_NAMESPACE,
        username: env.NACOS_USERNAME,
        password: env.NACOS_PASSWORD,
        group: env.NACOS_GROUP,
        dataId: env.NACOS_DATA_ID,
    };
}

/**
 * 初始化Nacos配置服务
 */
export async function initializeNacosConfig(): Promise<void> {
    const nacosConfig = getNacosConfig();
    if (!nacosConfig) {
        Logger.info('Nacos configuration not provided, using local environment variables only');
        return;
    }

    try {
        // 动态导入NacosConfigService避免循环依赖
        const { NacosConfigService } = await import('@/infrastructure/config/nacos-service');
        nacosConfigService = new NacosConfigService(nacosConfig);
        Logger.info('Nacos configuration service initialized successfully');

        // 尝试从Nacos获取配置并合并到本地环境变量
        await loadConfigFromNacos();
    } catch (error) {
        Logger.error('Failed to initialize Nacos configuration service:', {}, error as Error);
        throw error;
    }
}

/**
 * 从Nacos加载配置并合并到环境变量
 */
async function loadConfigFromNacos(): Promise<void> {
    if (!nacosConfigService) {
        return;
    }

    try {
        const nacosConfig = getNacosConfig();
        if (!nacosConfig) {
            return;
        }

        const configContent = await nacosConfigService.getConfig(
            nacosConfig.dataId!,
            nacosConfig.group
        );

        if (configContent) {
            // 解析配置内容（假设是JSON格式）
            const parsedConfig = JSON.parse(configContent) as unknown;
            
            // 类型检查，确保是对象
            if (
                parsedConfig !== null 
                && parsedConfig !== undefined 
                && typeof parsedConfig === 'object' 
                && !Array.isArray(parsedConfig)
            ) {
                const remoteConfig = parsedConfig as Record<string, unknown>;
                
                // 将远程配置合并到process.env
                Object.keys(remoteConfig).forEach(key => {
                    const value = remoteConfig[key];
                    if (value !== undefined && value !== null) {
                        process.env[key] = String(value);
                    }
                });

                Logger.info('Configuration loaded from Nacos successfully');
                
                // 重新解析环境配置以使用Nacos配置
                refreshEnvironment();
            } else {
                Logger.warn('Invalid configuration format from Nacos, expected object');
            }
        }
    } catch (error) {
        Logger.error('Failed to load configuration from Nacos:', {}, error as Error);
        throw error;
    }
}

/**
 * 获取Nacos配置服务实例
 */
export function getNacosConfigService(): INacosConfigService | null {
    return nacosConfigService;
}

/**
 * 获取布尔类型的环境变量
 */
export function getEnvVarAsBoolean(key: string, defaultValue?: boolean): boolean {
    const value = process.env[key];
    if ((value ?? '').trim() === '') {
        if (defaultValue !== undefined) {
            return defaultValue;
        }
        throw new Error(`Environment variable ${key} is not set`);
    }

    return (value ?? '').toLowerCase() === 'true';
}

/**
 * 获取数字类型的环境变量
 */
export function getEnvVarAsNumber(key: string, defaultValue?: number): number {
    const value = process.env[key];
    if ((value ?? '').trim() === '') {
        if (defaultValue !== undefined) {
            return defaultValue;
        }
        throw new Error(`Environment variable ${key} is not set`);
    }

    const parsedValue = parseInt(value!, 10);
    if (isNaN(parsedValue)) {
        throw new Error(`Environment variable ${key} is not a valid number: ${value}`);
    }

    return parsedValue;
} 