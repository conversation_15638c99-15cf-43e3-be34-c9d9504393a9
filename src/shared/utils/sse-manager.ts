/**
 * SSE连接管理工具
 * 
 * 提供SSE连接的安全管理和错误处理功能
 */

import type { Session } from 'better-sse';
import type { TypedResponse } from '@/shared/types';
import { logInfo } from '@/infrastructure/logger';
import { createSessionErrorEvent } from './message-converter';

/**
 * SSE连接上下文信息
 */
export interface SSEConnectionContext {
    requestId: string;
    userId: string;
    sessionId: string;
}

/**
 * 安全关闭SSE连接
 */
export function safeCloseSSEConnection(
    res: TypedResponse,
    context: SSEConnectionContext,
    reason: string
): void {
    try {
        res.end();
        logInfo(`SSE connection closed: ${reason}`, {
            requestId: context.requestId,
            userId: context.userId,
            sessionId: context.sessionId,
        });
    } catch (error) {
        logInfo('Connection already closed or error occurred while closing', {
            requestId: context.requestId,
            userId: context.userId,
            sessionId: context.sessionId,
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}

/**
 * 发送SSE错误事件并关闭连接
 */
export function sendSSEErrorAndClose(
    sseSession: Session,
    res: TypedResponse,
    context: SSEConnectionContext,
    errorType: string
): void {
    if (sseSession.isConnected) {
        const sessionErrorEvent = createSessionErrorEvent(context.sessionId, errorType);
        sseSession.push(sessionErrorEvent, 'session-error');
        
        logInfo('Session error sent, closing connection immediately', {
            requestId: context.requestId,
            userId: context.userId,
            sessionId: context.sessionId,
            errorType,
        });
        
        safeCloseSSEConnection(res, context, `Error: ${errorType}`);
    }
}

/**
 * 发送会话完成事件并关闭连接
 */
export function sendSessionFinishAndClose(
    sseSession: Session,
    res: TypedResponse,
    context: SSEConnectionContext
): void {
    if (sseSession.isConnected) {
        logInfo('Session finished, closing connection immediately', {
            requestId: context.requestId,
            userId: context.userId,
            sessionId: context.sessionId,
        });

        safeCloseSSEConnection(res, context, 'Session completed');
    }
}

/**
 * 检查SSE连接状态
 */
export function isSSEConnected(sseSession: Session | undefined): boolean {
    return sseSession?.isConnected === true;
}
