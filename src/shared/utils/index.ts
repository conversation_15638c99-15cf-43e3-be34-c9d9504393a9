import { randomBytes, randomUUID } from 'crypto';
import type { LogContext } from '@/shared/types';
import {
    logError as _logError,
    logInfo as _logInfo,
    logWarn as _logWarn,
    logDebug as _logDebug
} from '@/infrastructure/logger';

/**
 * Generate a random string of specified length
 */
export const generateRandomString = (length = 16): string => {
    return randomBytes(Math.ceil(length / 2))
        .toString('hex')
        .slice(0, length);
};

/**
 * Generate a UUID v4
 */
export const generateUUID = (): string => {
    return randomUUID();
};

/**
 * Generate a secure random token
 */
export const generateSecureToken = (length = 32): string => {
    return randomBytes(length).toString('hex');
};

/**
 * Sleep for specified milliseconds
 */
export const sleep = (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Measure execution time of a function
 */
export const measureTime = async <T>(
    fn: () => Promise<T>,
): Promise<{ result: T; duration: number }> => {
    const start = Date.now();
    const result = await fn();
    const duration = Date.now() - start;

    return { result, duration };
};

/**
 * Generate a random number between min and max
 */
export const generateRandomNumber = (min = 0, max = 1000000): number => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * Get current timestamp
 */
export const getCurrentTimestamp = (): number => Date.now();

/**
 * Safe JSON parsing
 */
export const safeJsonParse = <T = unknown>(jsonString: string, defaultValue: T): T => {
    try {
        return JSON.parse(jsonString) as T;
    } catch {
        return defaultValue;
    }
};

/**
 * Delay for specified milliseconds
 */
export const delay = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Array chunking
 */
export const chunk = <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
};

/**
 * Safely create LogContext with only defined values
 * This handles exactOptionalPropertyTypes: true in TypeScript config
 */
export function createLogContext(context: {
    requestId?: string | undefined;
    userId?: string | undefined;
    method?: string | undefined;
    url?: string | undefined;
    statusCode?: number | undefined;
    duration?: number | undefined;
    userAgent?: string | undefined;
    ip?: string | undefined;
    host?: string | undefined;
    port?: number | undefined;
    database?: string | undefined;
    processId?: number | undefined;
    [key: string]: unknown;
}): LogContext {
    const logContext: LogContext = {};

    // Only add properties that have defined values
    if (context.requestId !== undefined) {
        logContext.requestId = context.requestId;
    }

    if (context.userId !== undefined) {
        logContext.userId = context.userId;
    }

    if (context.method !== undefined) {
        logContext.method = context.method;
    }

    if (context.url !== undefined) {
        logContext.url = context.url;
    }

    if (context.statusCode !== undefined) {
        logContext.statusCode = context.statusCode;
    }

    if (context.duration !== undefined) {
        logContext.duration = context.duration;
    }

    if (context.userAgent !== undefined) {
        logContext.userAgent = context.userAgent;
    }

    if (context.ip !== undefined) {
        logContext.ip = context.ip;
    }

    if (context.host !== undefined) {
        logContext.host = context.host;
    }

    if (context.port !== undefined) {
        logContext.port = context.port;
    }

    if (context.database !== undefined) {
        logContext.database = context.database;
    }

    if (context.processId !== undefined) {
        logContext.processId = context.processId;
    }

    // Add any other properties
    Object.keys(context).forEach(key => {
        if (!['requestId', 'userId', 'method', 'url', 'statusCode', 'duration',
            'userAgent', 'ip', 'host', 'port', 'database', 'processId'].includes(key)) {
            if (context[key] !== undefined) {
                logContext[key] = context[key];
            }
        }
    });

    return logContext;
}

/**
 * Safe logging functions that handle exactOptionalPropertyTypes
 */

/**
 * Safe error logging
 */
export function logError(
    message: string,
    context?: {
        requestId?: string | undefined;
        userId?: string | undefined;
        [key: string]: unknown;
    },
    error?: Error
): void {
    const safeContext = context ? createLogContext(context) : undefined;
    _logError(message, safeContext, error);
}

/**
 * Safe info logging
 */
export function logInfo(
    message: string,
    context?: {
        requestId?: string | undefined;
        userId?: string | undefined;
        [key: string]: unknown;
    }
): void {
    const safeContext = context ? createLogContext(context) : undefined;
    _logInfo(message, safeContext);
}

/**
 * Safe warn logging
 */
export function logWarn(
    message: string,
    context?: {
        requestId?: string | undefined;
        userId?: string | undefined;
        [key: string]: unknown;
    }
): void {
    const safeContext = context ? createLogContext(context) : undefined;
    _logWarn(message, safeContext);
}

/**
 * Safe debug logging
 */
export function logDebug(
    message: string,
    context?: {
        requestId?: string | undefined;
        userId?: string | undefined;
        [key: 'errors' | string]: unknown;
    }
): void {
    const safeContext = context ? createLogContext(context) : undefined;
    _logDebug(message, safeContext);
}

// Export A2A client
export { XuiA2AClient } from './xui-a2a-client';

/**
 * Shared Utilities
 * 
 * Centralized exports for all utility functions and classes.
 */

// Base components for controllers and services
export * from './base-controller';

// Utility functions
export * from './crypto';
export * from './env';
export * from './errors';
export * from './langfuse-tracer';
export * from './message-converter';
export * from './response-formatter';
export * from './sse-manager';
export * from './time';
export * from './validator-helpers';
