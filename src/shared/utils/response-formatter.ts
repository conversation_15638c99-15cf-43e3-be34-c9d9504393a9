/**
 * Response Formatter Utilities
 * 
 * Standardized API response formatting.
 */

import type { Request, Response } from 'express';
import type { ApiResponse, PaginatedResponse, PaginationMetadata } from '@/shared/types';
import { StatusCodes } from 'http-status-codes';
import { getDateTime } from './time';

/**
 * Create success response
 */
export function createSuccessResponse<T>(
    data: T,
    message = 'Success'
): ApiResponse<T> {
    return {
        success: true,
        message,
        data,
        timestamp: getDateTime(new Date()),
    };
}

/**
 * Create error response
 */
export function createErrorResponse(
    message = 'An error occurred',
    details?: string | object
): ApiResponse<null> {
    return {
        success: false,
        message,
        data: null,
        ...(details !== undefined && { error: details }),
        timestamp: getDateTime(new Date()),
    };
}

/**
 * Create paginated response
 */
export function createPaginatedResponse<T>(
    data: T[],
    pagination: PaginationMetadata,
    message = 'Data retrieved successfully'
): PaginatedResponse<T> {
    return {
        success: true,
        message,
        data,
        pagination,
        timestamp: getDateTime(new Date()),
    };
}

/**
 * Send success response
 */
export function sendSuccessResponse<T>(
    res: Response,
    data: T,
    message = 'Success',
    status = StatusCodes.OK
): void {
    const response = createSuccessResponse(data, message);
    res.status(status).json(response);
}

/**
 * Send error response
 */
export function sendErrorResponse(
    res: Response,
    message = 'An error occurred',
    details?: string | object,
    status: number = StatusCodes.INTERNAL_SERVER_ERROR
): void {
    const response = createErrorResponse(message, details);
    res.status(status).json(response);
}

/**
 * Send paginated response
 */
export function sendPaginatedResponse<T>(
    res: Response,
    data: T[],
    pagination: PaginationMetadata,
    message = 'Data retrieved successfully'
): void {
    const response = createPaginatedResponse(data, pagination, message);
    res.status(StatusCodes.OK).json(response);
}

/**
 * Send validation error response
 */
export function sendValidationError(
    res: Response,
    errors: object | string,
    message = 'Validation failed'
): void {
    sendErrorResponse(res, message, errors, StatusCodes.BAD_REQUEST);
}

/**
 * Send not found response
 */
export function sendNotFoundResponse(
    res: Response,
    resource = 'Resource',
    message?: string
): void {
    const errorMessage = message ?? `${resource} not found`;
    sendErrorResponse(res, errorMessage, undefined, StatusCodes.NOT_FOUND);
}

/**
 * Send unauthorized response
 */
export function sendUnauthorizedResponse(
    res: Response,
    message = 'Unauthorized access'
): void {
    sendErrorResponse(res, message, undefined, StatusCodes.UNAUTHORIZED);
}

/**
 * Send forbidden response
 */
export function sendForbiddenResponse(
    res: Response,
    message = 'Access forbidden'
): void {
    sendErrorResponse(res, message, undefined, StatusCodes.FORBIDDEN);
}

/**
 * Send conflict response
 */
export function sendConflictResponse(
    res: Response,
    message = 'Resource conflict',
    details?: string | object
): void {
    sendErrorResponse(res, message, details, StatusCodes.CONFLICT);
}

/**
 * Send bad request response
 */
export function sendBadRequestResponse(
    res: Response,
    message = 'Bad request',
    details?: string | object
): void {
    sendErrorResponse(res, message, details, StatusCodes.BAD_REQUEST);
}

/**
 * Extract pagination info from request
 */
export function extractPaginationFromRequest(req: Request): {
    page: number;
    limit: number;
    offset: number;
} {
    const pageParam = req.query['page'] as string | undefined;
    const limitParam = req.query['limit'] as string | undefined;
    
    const page = Math.max(1, parseInt(pageParam ?? '1', 10));
    const limit = Math.min(100, Math.max(1, parseInt(limitParam ?? '10', 10)));
    const offset = (page - 1) * limit;

    return { page, limit, offset };
}

/**
 * Create pagination metadata
 */
export function createPaginationMetadata(
    page: number,
    limit: number,
    total: number
): PaginationMetadata {
    const totalPages = Math.ceil(total / limit);
    
    return {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
    };
}
