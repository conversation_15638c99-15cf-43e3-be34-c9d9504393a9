/**
 * Validator Helpers
 * 
 * Common validation utilities and helpers.
 */

import { z } from 'zod/v4';

/**
 * Common validation schemas
 */
export const commonSchemas = {
    uuid: z.uuid(),
    email: z.email(),
    url: z.url(),
    
    // Pagination schemas
    pagination: z.object({
        page: z.string().transform((val) => parseInt(val, 10)).pipe(z.number().min(1)).optional(),
        limit: z.string().transform((val) => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),
        sortBy: z.string().optional(),
        sortOrder: z.enum(['asc', 'desc']).optional(),
    }),

    // Common string validations
    nonEmptyString: z.string().trim().min(1, 'Field cannot be empty'),
    optionalString: z.string().trim().optional(),
    
    // Date validations
    dateString: z.iso.datetime(),
    optionalDateString: z.iso.datetime().optional(),
    
    // Number validations
    positiveNumber: z.number().positive('Must be a positive number'),
    nonNegativeNumber: z.number().min(0, 'Must be non-negative'),
    
    // ID validations
    numericId: z.string().transform((val) => parseInt(val, 10)).pipe(z.number().positive()),
    stringId: z.string().min(1, 'ID cannot be empty'),
    
    // Sanitized string (替代 sanitizeString 函数)
    sanitizedString: z.string().trim().transform(str => str.replace(/\s+/g, ' ')),
};

/**
 * Validation error details
 */
export interface ValidationErrorDetails {
    field: string;
    message: string;
    code: string;
    received?: unknown;
}

/**
 * Format Zod validation issues for API response
 */
export function formatZodErrors(error: z.ZodError): ValidationErrorDetails[] {
    return error.issues.map(issue => {
        const details: ValidationErrorDetails = {
            field: issue.path.join('.'),
            message: issue.message,
            code: issue.code,
        };
        
        // 安全地检查和处理 received 属性，避免类型转换
        if (issue.code === 'invalid_type' && 'received' in issue) {
            const issueWithReceived = issue as unknown as { received?: unknown };
            details.received = issueWithReceived.received;
        }
        
        return details;
    });
}

/**
 * Check if value is empty or whitespace
 * 保留此函数因为它处理多种类型的空值检查
 */
export function isEmpty(value: unknown): boolean {
    if (value === null || value === undefined) {
        return true;
    }
    if (typeof value === 'string') {
        return value.trim().length === 0;
    }
    if (Array.isArray(value)) {
        return value.length === 0;
    }
    if (typeof value === 'object') {
        return Object.keys(value).length === 0;
    }
    return false;
}

/**
 * 验证工具函数 - 直接使用 Zod 的 safeParse
 * 
 * 使用示例:
 * const result = z.email().safeParse("<EMAIL>");
 * if (result.success) { console.log(result.data); }
 * else { console.log(formatZodErrors(result.error)); }
 */

/**
 * 中间件验证函数 - 用于 Express.js 路由
 */
export function createValidator<T>(schema: z.ZodType<T>, errorMessage: string = 'Validation failed') {
    return (data: unknown): T => {
        const result = schema.safeParse(data);
        
        if (result.success) {
            return result.data;
        }
        
        const details = formatZodErrors(result.error);
        const validationError = new Error(errorMessage) as Error & { code: string; details: ValidationErrorDetails[] };
        validationError.code = 'VALIDATION_ERROR';
        validationError.details = details;
        throw validationError;
    };
}