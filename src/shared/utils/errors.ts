/**
 * Error Handling Utilities
 * 
 * Custom error classes and error handling utilities.
 */

import getLangfuseService from '@/infrastructure/logger/langfuse';
import { Logger } from '@/infrastructure/logger/winston-logger';

const langfuse = getLangfuseService();

/**
 * Base application error class
 */
export class AppError extends Error {
    public readonly isOperational: boolean;
    public readonly statusCode: number;
    public readonly details?: unknown;

    constructor(
        message: string,
        statusCode = 500,
        isOperational = true,
        details?: unknown
    ) {
        super(message);
        
        this.name = this.constructor.name;
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.details = details;

        Error.captureStackTrace(this, this.constructor);
    }
}

/**
 * Validation error
 */
export class ValidationError extends AppError {
    constructor(message: string, details?: unknown) {
        super(message, 400, true, details);
    }
}

/**
 * Authentication error
 */
export class AuthenticationError extends AppError {
    constructor(message = 'Authentication failed') {
        super(message, 401, true);
    }
}

/**
 * Authorization error
 */
export class AuthorizationError extends AppError {
    constructor(message = 'Access forbidden') {
        super(message, 403, true);
    }
}

/**
 * Not found error
 */
export class NotFoundError extends AppError {
    constructor(resource = 'Resource', message?: string) {
        const errorMessage = message ?? `${resource} not found`;
        super(errorMessage, 404, true);
    }
}

/**
 * Conflict error
 */
export class ConflictError extends AppError {
    constructor(message = 'Resource conflict', details?: unknown) {
        super(message, 409, true, details);
    }
}

/**
 * Database error
 */
export class DatabaseError extends AppError {
    constructor(message = 'Database operation failed', details?: unknown) {
        super(message, 500, true, details);
    }
}

/**
 * External service error
 */
export class ExternalServiceError extends AppError {
    constructor(service: string, message?: string, details?: unknown) {
        const errorMessage = message ?? `${service} service unavailable`;
        super(errorMessage, 503, true, details);
    }
}

/**
 * Rate limit error
 */
export class RateLimitError extends AppError {
    constructor(message = 'Rate limit exceeded') {
        super(message, 429, true);
    }
}

/**
 * Timeout error
 */
export class TimeoutError extends AppError {
    constructor(operation = 'Operation', timeout?: number) {
        const message = timeout 
            ? `${operation} timed out after ${timeout}ms`
            : `${operation} timed out`;
        super(message, 408, true);
    }
}

/**
 * Check if error is operational (safe to expose to user)
 */
export function isOperationalError(error: Error): boolean {
    if (error instanceof AppError) {
        return error.isOperational;
    }
    return false;
}

/**
 * Get error status code
 */
export function getErrorStatusCode(error: Error): number {
    if (error instanceof AppError) {
        return error.statusCode;
    }
    
    // Handle Express body-parser errors (JSON parsing, etc.)
    const bodyParserError = error as Error & { 
        statusCode?: number; 
        status?: number; 
        type?: string;
    };
    if (bodyParserError.type === 'entity.parse.failed' || 
        bodyParserError.statusCode === 400 || 
        bodyParserError.status === 400) {
        return 400;
    }
    
    // Handle validation errors (e.g., from Zod or custom validators)
    const validationError = error as Error & { code?: string };
    if (validationError.code === 'VALIDATION_ERROR') {
        return 400;
    }
    
    // Handle built-in JavaScript error types
    if (error instanceof SyntaxError) {
        return 400; // Bad request for syntax errors
    }
    
    if (error instanceof TypeError && error.message.includes('validation')) {
        return 400; // Bad request for type validation errors
    }
    
    if (error instanceof RangeError) {
        return 400; // Bad request for range errors
    }
    
    // Default to 500 for unknown errors
    return 500;
}

/**
 * Get safe error message for user
 */
export function getSafeErrorMessage(error: Error): string {
    // Handle Express body-parser JSON errors specifically
    const bodyParserError = error as Error & { 
        type?: string; 
        body?: string;
        statusCode?: number;
    };
    
    if (bodyParserError.type === 'entity.parse.failed') {
        if (error instanceof SyntaxError) {
            // Extract useful information from JSON syntax error
            const match = error.message.match(/at position (\d+)/);
            const position = match ? match[1] : 'unknown';
            return `Invalid JSON format: ${error.message}. Check your request body syntax at position ${position}.`;
        }
        return `Invalid request body format: ${error.message}`;
    }
    
    // In development, always show the actual error message for debugging
    if (process.env['NODE_ENV'] === 'development') {
        return error.message;
    }
    
    // In production, only show operational errors to users
    if (isOperationalError(error)) {
        return error.message;
    }
    
    // Handle validation errors (these are usually safe to show)
    const validationError = error as Error & { code?: string };
    if (validationError.code === 'VALIDATION_ERROR') {
        return error.message;
    }
    
    // For non-operational errors in production, return generic message
    return 'An internal server error occurred';
}

/**
 * Log error with context
 */
export function logError(
    error: Error,
    context?: {
        requestId?: string;
        userId?: string;
        operation?: string;
        details?: unknown;
    }
): void {
    const errorContext = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        statusCode: getErrorStatusCode(error),
        isOperational: isOperationalError(error),
        ...context,
    };

    Logger.error('Application error occurred', errorContext, error);

    // Track error in Langfuse if available
    if (langfuse.isEnabled()) {
        langfuse.createEvent(
            'error',
            {
                error: error.message,
                type: error.name,
                statusCode: getErrorStatusCode(error),
            }
        );
    }
}

/**
 * Wrap async function with error handling
 */
export function asyncErrorHandler<T extends unknown[], R>(
    fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
        try {
            return await fn(...args);
        } catch (error) {
            logError(error as Error);
            throw error;
        }
    };
}

/**
 * Create error from unknown value
 */
export function createErrorFromUnknown(unknown: unknown): Error {
    if (unknown instanceof Error) {
        return unknown;
    }
    
    if (typeof unknown === 'string') {
        return new Error(unknown);
    }
    
    if (typeof unknown === 'object' && unknown !== null) {
        try {
            return new Error(JSON.stringify(unknown));
        } catch {
            return new Error('Unknown error object');
        }
    }
    
    return new Error(`Unknown error: ${String(unknown)}`);
}

/**
 * Retry function with exponential backoff
 */
export async function withRetry<T>(
    fn: () => Promise<T>,
    options: {
        maxRetries?: number;
        baseDelay?: number;
        maxDelay?: number;
        backoffFactor?: number;
    } = {}
): Promise<T> {
    const {
        maxRetries = 3,
        baseDelay = 1000,
        maxDelay = 30000,
        backoffFactor = 2,
    } = options;

    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            return await fn();
        } catch (error) {
            lastError = createErrorFromUnknown(error);
            
            if (attempt === maxRetries) {
                break;
            }

            const delay = Math.min(
                baseDelay * Math.pow(backoffFactor, attempt),
                maxDelay
            );

            Logger.warn(`Retry attempt ${attempt + 1}/${maxRetries + 1} after ${delay}ms`, {
                error: lastError.message,
                attempt: attempt + 1,
                maxRetries: maxRetries + 1,
                delay,
            });

            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    throw lastError!;
}
