/**
 * Cryptographic Utilities
 * 
 * Provides UUID generation, hashing, and encryption functions.
 */

import { randomUUID, createHash, createHmac, randomBytes } from 'crypto';

/**
 * Generate a UUID v4
 */
export const generateUUID = (): string => {
    return randomUUID();
};

/**
 * Generate a secure random string
 */
export const generateRandomString = (length: number = 32): string => {
    return randomBytes(length).toString('hex');
};

/**
 * Create SHA-256 hash
 */
export const createSHA256Hash = (data: string): string => {
    return createHash('sha256').update(data).digest('hex');
};

/**
 * Create HMAC-SHA256 signature
 */
export const createHMACSignature = (data: string, secret: string): string => {
    return createHmac('sha256', secret).update(data).digest('hex');
};

/**
 * Verify HMAC-SHA256 signature
 */
export const verifyHMACSignature = (data: string, signature: string, secret: string): boolean => {
    const expectedSignature = createHMACSignature(data, secret);
    return signature === expectedSignature;
};

/**
 * Create a hash of multiple values (useful for creating composite keys)
 */
export const createCompositeHash = (...values: string[]): string => {
    const combined = values.join('|');
    return createSHA256Hash(combined);
}; 