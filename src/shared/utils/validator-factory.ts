/**
 * 通用验证器工厂
 * 
 * 提供用于创建 Express 中间件的通用工厂函数，
 * 这些中间件用于验证请求的不同部分（如 params, body, query）。
 */

import type { Request, Response, NextFunction, RequestHandler } from 'express';
import { z } from 'zod/v4';
import { type AuthenticatedRequest } from '@/shared/types';
import { createValidator } from './validator-helpers';
import { createErrorResponse } from './response-formatter';
import { StatusCodes } from 'http-status-codes';

/**
 * 创建一个通用验证器中间件的工厂函数
 * @param part - 要验证的请求部分 ('params', 'body', 'query')
 * @param schema - Zod 验证 schema
 * @param description - 错误描述
 * @returns Express RequestHandler
 */
function createGenericValidator<T>(
    part: 'params' | 'body' | 'query',
    schema: z.ZodSchema<T>,
    description: string
): RequestHandler {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const validator = createValidator(schema, `无效的 ${description}`);
            const authReq = req as AuthenticatedRequest;
            const source = req[part] as Record<string, unknown>;

            const validatedData = validator(source) as Record<string, unknown>;

            switch (part) {
                case 'params':
                    authReq.validatedParams = { ...(authReq.validatedParams ?? {}), ...validatedData };
                    break;
                case 'body':
                    authReq.validatedBody = validatedData;
                    break;

                case 'query':
                    authReq.validatedQuery = validatedData;
                    break;
            }

            next();
        } catch (error) {
            const validationError = error as Error & { code?: string; details?: string | object };
            if (validationError.code === 'VALIDATION_ERROR') {
                res.status(
                    StatusCodes.BAD_REQUEST
                ).json(
                    createErrorResponse(validationError.message, validationError.details)
                );
            } else {
                res.status(
                    StatusCodes.BAD_REQUEST
                ).json(
                    createErrorResponse(`无效的 ${description}`, validationError.message)
                );
            }
        }
    };
}

/**
 * 创建用于验证 URL 参数的 Express 中间件
 */
export function createParamsValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return createGenericValidator('params', schema, description);
}

/**
 * 创建用于验证请求体的 Express 中间件
 */
export function createBodyValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return createGenericValidator('body', schema, description);
}

/**
 * 创建用于验证查询参数的 Express 中间件
 */
export function createQueryValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return createGenericValidator('query', schema, description);
} 