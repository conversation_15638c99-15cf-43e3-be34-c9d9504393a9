/**
 * Nacos Types
 * 
 * Contains type definitions for Nacos configuration center integration.
 */

import type { NacosConfigClient } from 'nacos';

export interface NacosConfig {
    enabled: boolean;
    serverAddr: string;
    namespace?: string | undefined;
    username?: string | undefined;
    password?: string | undefined;
    endpoint?: string | undefined;
    accessKey?: string | undefined;
    secretKey?: string | undefined;
    requestTimeout?: number | undefined;
    group?: string | undefined;
    dataId?: string | undefined;
}

export interface NacosConfigItem {
    dataId: string;
    group?: string;
    timeout?: number;
}

export interface INacosConfigService {
    getClient(): NacosConfigClient;
    getConfig(dataId: string, group?: string): Promise<string>;
    publishConfig(dataId: string, group: string, content: string): Promise<boolean>;
    removeConfig(dataId: string, group?: string): Promise<boolean>;
    subscribe(config: NacosConfigItem, listener: (content: string) => void): void;
    unsubscribe(config: NacosConfigItem, listener?: (content: string) => void): void;
} 