/**
 * Database Type Definitions
 * 
 * Contains all database-related interfaces and types.
 */

// Database Configuration
export interface DatabaseConfig {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    ssl: boolean;
    max?: number;
    idleTimeoutMillis?: number;
    connectionTimeoutMillis?: number;
}

// Database Connection
export interface DatabaseConnection {
    isConnected: boolean;
    connectionString: string;
    pool?: {
        total: number;
        idle: number;
        active: number;
    };
}

// Query Result
export interface QueryResult<T = unknown> {
    data: T[];
    rowCount: number;
    affectedRows?: number;
    insertId?: string | number;
}

// Transaction Options
export interface TransactionOptions {
    isolationLevel?: 'READ_UNCOMMITTED' | 'READ_COMMITTED' | 'REPEATABLE_READ' | 'SERIALIZABLE';
    timeout?: number;
    readOnly?: boolean;
}

// Base Service Interface
export interface BaseService {
    name: string;
    initialize(): Promise<void>;
    destroy(): Promise<void>;
    healthCheck(): Promise<ServiceHealth>;
}

export interface ServiceHealth {
    status: 'healthy' | 'unhealthy';
    responseTime?: number;
    error?: string;
}

// Repository Base Interface
export interface BaseRepository<T, ID = string> {
    findById(id: ID): Promise<T | null>;
    findAll(): Promise<T[]>;
    create(data: Partial<T>): Promise<T>;
    update(id: ID, data: Partial<T>): Promise<T | null>;
    delete(id: ID): Promise<boolean>;
}

// Database Query Options
export interface QueryOptions {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    filters?: Record<string, unknown>;
} 