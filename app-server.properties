# ===================================================================
# XUI App Server - Production Configuration
# ===================================================================
# This file contains the complete configuration for the application.
# It should be managed in the Nacos configuration center.
# ===================================================================


# -------------------------------------------------------------------
# Database Configuration
# -------------------------------------------------------------------
# Replace with your production database credentials.
DB_HOST=your_production_db_host
DB_PORT=5432
DB_USER=your_production_db_user
DB_PASSWORD=your_secure_production_db_password
DB_NAME=xui_prod
DB_SSL=true # Recommended for production environments

# Database connection pool settings
DB_MAX_CONNECTIONS=50
DB_IDLE_TIMEOUT=30000 # (30 seconds)
DB_CONNECTION_TIMEOUT=10000 # (10 seconds)


# -------------------------------------------------------------------
# Server Configuration
# -------------------------------------------------------------------
HOST=0.0.0.0 # Binds to all available network interfaces
PORT=3000


# -------------------------------------------------------------------
# Logging Configuration
# -------------------------------------------------------------------
# 'info' is a good default for production.
# Other options: 'debug', 'warn', 'error'
LOG_LEVEL=info

# Directory where log files will be stored on the server.
# Ensure this directory exists and has the correct write permissions.
LOG_DIR=/var/log/xui-app-server

# Log format. 'json' is recommended for structured logging in production.
# 'console' can be used for a more human-readable format.
LOG_FORMAT=json


# -------------------------------------------------------------------
# Security Configuration
# -------------------------------------------------------------------
# It is critical to use a strong, randomly generated secret.
# You can generate one using: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
JWT_SECRET=your_long_and_random_jwt_secret

# Higher value is more secure but slower. 12 is a strong default.
BCRYPT_ROUNDS=12

# Rate limiting to prevent abuse.
# Allow 100 requests per 15-minute window from a single IP.
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100


# -------------------------------------------------------------------
# CORS (Cross-Origin Resource Sharing) Configuration
# -------------------------------------------------------------------
# Specify the exact domain of your web client.
# Using a wildcard (*) is highly discouraged in production.
CORS_ORIGIN=https://your-frontend-domain.com
CORS_CREDENTIALS=true


# -------------------------------------------------------------------
# Langfuse Monitoring (Optional)
# -------------------------------------------------------------------
# If you use Langfuse for tracing, fill these in.
# Otherwise, the feature will be disabled.
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key
LANGFUSE_SECRET_KEY=your_langfuse_secret_key
LANGFUSE_HOST=https://langfuse.your-company.com # (Optional)


# -------------------------------------------------------------------
# Redis Cache (Optional)
# -------------------------------------------------------------------
# If you use Redis for caching or other features, uncomment and fill these in.
# REDIS_URL=redis://your_redis_host:6379
# REDIS_PASSWORD=your_redis_password


# -------------------------------------------------------------------
# HSTS (HTTP Strict Transport Security)
# -------------------------------------------------------------------
# Enable HSTS for enhanced security. This forces browsers to use HTTPS.
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000 # (1 year)