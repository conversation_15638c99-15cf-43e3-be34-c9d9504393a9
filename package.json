{"name": "xui-app-server", "version": "1.0.0", "description": "Modern TypeScript Express application with Drizzle ORM and PostgreSQL", "main": "dist/src/index.js", "type": "module", "engines": {"node": ">=20.0.0", "pnpm": ">=9.0.0"}, "scripts": {"dev": "nodemon", "build": "tsc", "start": "tsx src/index.ts", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "deploy": "./scripts/quick-deploy.sh", "test:jest": "jest", "test:jest:watch": "jest --watch", "test:jest:coverage": "jest --coverage", "test:langfuse": "tsx scripts/test-langfuse.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "pnpm clean"}, "keywords": ["typescript", "express", "drizzle", "postgresql", "zod", "modern", "api"], "author": "", "license": "MIT", "dependencies": {"@a2a-js/sdk": "^0.2.2", "@xui-web-app/a2u": "^0.2.4", "better-sse": "^0.15.1", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.6.0", "drizzle-orm": "^0.44.2", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "http-status-codes": "^2.3.0", "langfuse": "^3.37.6", "morgan": "^1.10.0", "nacos": "^2.6.0", "postgres": "^3.4.7", "reflect-metadata": "^0.2.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsconfig-paths": "^4.2.0", "tsyringe": "^4.10.0", "winston": "^3.17.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/morgan": "^1.9.10", "@types/node": "^22.15.33", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "cross-env": "^7.0.3", "drizzle-kit": "^0.31.3", "drizzle-zod": "^0.5.1", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "jest": "^29.7.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "rimraf": "^6.0.1", "shx": "^0.4.0", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.15.0", "pnpm": {"peerDependencyRules": {"ignoreMissing": ["@types/*"]}}}