{"semi": true, "trailingComma": "all", "singleQuote": true, "printWidth": 100, "tabWidth": 4, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "embeddedLanguageFormatting": "auto", "overrides": [{"files": ["*.json", "*.jsonc"], "options": {"tabWidth": 4, "parser": "json"}}, {"files": ["package.json"], "options": {"tabWidth": 4, "parser": "json-stringify"}}]}