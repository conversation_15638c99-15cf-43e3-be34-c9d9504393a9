# 单元测试文档

本目录包含了 XUI App Server 项目的完整单元测试套件。

## 测试结构

```
tests/unit/
├── config/           # 配置模块测试
│   └── env.test.ts   # 环境配置测试
├── controllers/      # 控制器测试
│   └── agent.test.ts # Agent 控制器测试
├── middleware/       # 中间件测试
│   ├── auth.test.ts  # 认证中间件测试
│   └── validation.test.ts # 验证中间件测试
├── services/         # 服务层测试
│   ├── agent.test.ts    # Agent 服务测试
│   ├── session.test.ts  # Session 服务测试
│   └── message.test.ts  # Message 服务测试
├── utils/            # 工具函数测试
│   ├── index.test.ts           # 通用工具函数测试
│   ├── message-converter.test.ts # 消息转换器测试
│   ├── session-helpers.test.ts   # 会话辅助函数测试
│   └── errors.test.ts          # 错误处理测试
├── validators/       # 验证器测试
│   ├── agent.test.ts    # Agent 验证器测试
│   ├── session.test.ts  # Session 验证器测试
│   └── message.test.ts  # Message 验证器测试
└── README.md         # 本文档
```

## 测试覆盖范围

### 工具函数 (Utils)
- ✅ 通用工具函数 (`generateRandomString`, `generateUUID`, `createSuccessResponse` 等)
- ✅ 消息转换器 (A2U/A2A 协议转换)
- ✅ 会话辅助函数 (标题提取等)
- ✅ 错误处理类和重试机制

### 验证器 (Validators)
- ✅ Agent 验证模式 (创建、更新、查询参数)
- ✅ Session 验证模式 (会话管理、聊天请求)
- ✅ Message 验证模式 (消息内容、工具调用)

### 服务层 (Services)
- ✅ AgentService (CRUD 操作、分页、搜索)
- ✅ SessionService (会话管理、用户会话)
- ✅ MessageService (消息管理、统计)

### 控制器 (Controllers)
- ✅ AgentController (HTTP 请求处理、错误响应)
- 🔄 SessionController (待完善)
- 🔄 MessageController (待完善)

### 中间件 (Middleware)
- ✅ 认证中间件 (用户ID验证)
- ✅ 验证中间件 (请求数据验证)
- 🔄 监控中间件 (待完善)

### 配置模块 (Config)
- ✅ 环境配置加载和验证
- 🔄 日志配置 (待完善)

## 运行测试

### 运行所有单元测试
```bash
npm run test:unit
```

### 运行特定模块测试
```bash
# 运行工具函数测试
npm test -- tests/unit/utils

# 运行服务层测试
npm test -- tests/unit/services

# 运行验证器测试
npm test -- tests/unit/validators
```

### 运行单个测试文件
```bash
npm test -- tests/unit/utils/index.test.ts
```

### 生成测试覆盖率报告
```bash
npm run test:coverage
```

## 测试特点

### Mock 策略
- **数据库**: 使用 Jest mock 模拟 Drizzle ORM 查询
- **外部依赖**: Mock 第三方库和服务
- **日志系统**: Mock 日志函数避免测试输出干扰

### 测试数据
- 使用真实的 UUID 格式进行测试
- 模拟完整的请求/响应对象
- 包含边界条件和错误场景测试

### 断言策略
- 验证函数调用参数和次数
- 检查返回值结构和类型
- 测试错误处理和异常情况

## 最佳实践

### 测试命名
- 使用描述性的测试名称
- 遵循 "should [expected behavior] when [condition]" 格式
- 按功能分组测试用例

### 测试隔离
- 每个测试用例独立运行
- 使用 `beforeEach` 重置 mock 状态
- 避免测试间的依赖关系

### 错误测试
- 测试各种错误场景
- 验证错误消息和状态码
- 确保错误处理的完整性

## 待完善项目

1. **控制器测试扩展**
   - SessionController 完整测试
   - MessageController 完整测试

2. **中间件测试扩展**
   - 监控中间件测试
   - 错误处理中间件测试

3. **集成测试**
   - 数据库集成测试
   - API 端到端测试

4. **性能测试**
   - 负载测试
   - 内存泄漏测试

## 测试配置

测试使用以下配置：
- **测试框架**: Jest
- **TypeScript 支持**: ts-jest
- **覆盖率目标**: 80% (分支、函数、行、语句)
- **超时设置**: 10秒
- **环境**: Node.js

## 贡献指南

添加新测试时请遵循：
1. 为每个新功能编写对应测试
2. 保持测试覆盖率在 80% 以上
3. 包含正常和异常场景测试
4. 更新本文档说明新增测试内容
