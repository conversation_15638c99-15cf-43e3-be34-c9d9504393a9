import { NextFunction } from 'express';
import { validateUserId } from '@/middleware/auth';
import { StatusCodes } from 'http-status-codes';
import type { AuthenticatedRequest, TypedResponse } from '@/types';

// Mock logger
jest.mock('@/config/logger', () => ({
    logWarn: jest.fn(),
    logError: jest.fn(),
}));

describe('Auth Middleware', () => {
    let mockRequest: Partial<AuthenticatedRequest>;
    let mockResponse: Partial<TypedResponse>;
    let mockNext: NextFunction;

    beforeEach(() => {
        mockRequest = {
            requestId: 'test-request-id',
            method: 'GET',
            url: '/api/test',
            headers: {},
        };

        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis(),
        };

        mockNext = jest.fn();
        jest.clearAllMocks();
    });

    describe('validateUserId', () => {
        it('should pass validation with valid userId header', () => {
            mockRequest.headers = {
                'x-user-id': 'user-123',
            };

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockRequest.user).toEqual({
                id: 'user-123',
                email: '',
                roles: [],
            });
            expect(mockNext).toHaveBeenCalled();
            expect(mockResponse.status).not.toHaveBeenCalled();
        });

        it('should trim whitespace from userId', () => {
            mockRequest.headers = {
                'x-user-id': '  user-123  ',
            };

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockRequest.user).toEqual({
                id: 'user-123',
                email: '',
                roles: [],
            });
            expect(mockNext).toHaveBeenCalled();
        });

        it('should reject missing userId header', () => {
            mockRequest.headers = {};

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.UNAUTHORIZED);
            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    success: false,
                    message: 'Unauthorized',
                    error: 'Missing userId in request headers',
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                    timestamp: expect.any(String),
                    requestId: 'test-request-id',
                })
            );
            expect(mockNext).not.toHaveBeenCalled();
        });

        it('should reject empty userId header', () => {
            mockRequest.headers = {
                'x-user-id': '',
            };

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.UNAUTHORIZED);
            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    success: false,
                    message: 'Unauthorized',
                    error: 'Invalid userId in request headers',
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                    timestamp: expect.any(String),
                    requestId: 'test-request-id',
                })
            );
            expect(mockNext).not.toHaveBeenCalled();
        });

        it('should reject whitespace-only userId header', () => {
            mockRequest.headers = {
                'x-user-id': '   ',
            };

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.UNAUTHORIZED);
            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    success: false,
                    message: 'Unauthorized',
                    error: 'Invalid userId in request headers',
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                    timestamp: expect.any(String),
                    requestId: 'test-request-id',
                })
            );
            expect(mockNext).not.toHaveBeenCalled();
        });

        it('should reject non-string userId header', () => {
            mockRequest.headers = {
                'x-user-id': 123 as unknown as string,
            };

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.UNAUTHORIZED);
            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    success: false,
                    message: 'Unauthorized',
                    error: 'Invalid userId in request headers',
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                    timestamp: expect.any(String),
                    requestId: 'test-request-id',
                })
            );
            expect(mockNext).not.toHaveBeenCalled();
        });

        it('should handle array userId header by taking first value', () => {
            mockRequest.headers = {
                'x-user-id': ['user-123', 'user-456'],
            };

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockRequest.user).toEqual({
                id: 'user-123',
                email: '',
                roles: [],
            });
            expect(mockNext).toHaveBeenCalled();
        });

        it('should handle case-insensitive header names', () => {
            mockRequest.headers = {
                'X-User-ID': 'user-123',
            };

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockRequest.user).toEqual({
                id: 'user-123',
                email: '',
                roles: [],
            });
            expect(mockNext).toHaveBeenCalled();
        });

        it('should handle errors gracefully', () => {
            // Mock an error scenario
            const originalHeaders = mockRequest.headers;
            Object.defineProperty(mockRequest, 'headers', {
                get: () => {
                    throw new Error('Header access error');
                },
                configurable: true,
            });

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.INTERNAL_SERVER_ERROR);
            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    success: false,
                    message: 'Internal server error',
                    error: 'Authentication error',
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                    timestamp: expect.any(String),
                    requestId: 'test-request-id',
                })
            );
            expect(mockNext).not.toHaveBeenCalled();

            // Restore original headers
            Object.defineProperty(mockRequest, 'headers', {
                value: originalHeaders,
                configurable: true,
            });
        });

        it('should handle missing requestId gracefully', () => {
            delete mockRequest.requestId;
            mockRequest.headers = {
                'x-user-id': 'user-123',
            };

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockRequest.user).toEqual({
                id: 'user-123',
                email: '',
                roles: [],
            });
            expect(mockNext).toHaveBeenCalled();
        });

        it('should log warning for invalid userId', () => {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            const { logWarn } = require('@/config/logger');
            
            mockRequest.headers = {
                'x-user-id': '',
            };

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(logWarn).toHaveBeenCalledWith(
                'Invalid userId in request headers',
                {
                    requestId: 'test-request-id',
                    method: 'GET',
                    url: '/api/test',
                    userId: '',
                }
            );
        });

        it('should log error for exceptions', () => {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            const { logError } = require('@/config/logger');
            
            // Mock an error scenario
            Object.defineProperty(mockRequest, 'headers', {
                get: () => {
                    throw new Error('Header access error');
                },
                configurable: true,
            });

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(logError).toHaveBeenCalledWith(
                'Error in userId validation middleware',
                {
                    requestId: 'test-request-id',
                    method: 'GET',
                    url: '/api/test',
                },
                expect.any(Error)
            );
        });

        it('should handle undefined headers object', () => {
            mockRequest.headers = undefined as unknown as Record<string, string>;

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.UNAUTHORIZED);
            expect(mockNext).not.toHaveBeenCalled();
        });

        it('should handle null userId header', () => {
            mockRequest.headers = {
                'x-user-id': null as unknown as string,
            };

            validateUserId(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.UNAUTHORIZED);
            expect(mockNext).not.toHaveBeenCalled();
        });
    });
});
