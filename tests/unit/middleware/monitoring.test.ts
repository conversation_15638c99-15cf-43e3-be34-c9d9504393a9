import { NextFunction } from 'express';
import {
    requestMonitoring,
    activeConnectionsMonitoring,
    userActivityMonitoring,
    errorRateMonitoring,
    monitorDatabaseOperation,
    BusinessMetrics,
} from '@/middleware/monitoring';
import { metricsCollector } from '@/utils/metrics';
import type { AuthenticatedRequest, TypedResponse } from '@/types';

// Mock the metrics collector
jest.mock('@/utils/metrics', () => ({
    metricsCollector: {
        recordRequest: jest.fn(),
        recordTimer: jest.fn(),
        incrementCounter: jest.fn(),
        recordHistogram: jest.fn(),
        recordGauge: jest.fn(),
    },
}));

// Mock logger
jest.mock('@/config/logger', () => ({
    logInfo: jest.fn(),
    logError: jest.fn(),
}));

describe('Monitoring Middleware', () => {
    let mockRequest: Partial<AuthenticatedRequest>;
    let mockResponse: Partial<TypedResponse>;
    let mockNext: NextFunction;
    let mockMetricsCollector: jest.Mocked<typeof metricsCollector>;

    beforeEach(() => {
        mockRequest = {
            requestId: 'test-request-id',
            method: 'GET',
            path: '/api/test',
            route: { path: '/api/test' },
            user: { id: 'user-123', email: '<EMAIL>', roles: [] },
            get: jest.fn(),
        };

        mockResponse = {
            statusCode: 200,
            on: jest.fn(),
            get: jest.fn(),
        };

        mockNext = jest.fn();
        mockMetricsCollector = metricsCollector as jest.Mocked<typeof metricsCollector>;
        jest.clearAllMocks();
    });

    describe('requestMonitoring', () => {
        it('should record request metrics on successful response', () => {
            const mockGet = mockRequest.get as jest.MockedFunction<any>;
            mockGet.mockReturnValue('Mozilla/5.0 Chrome/91.0');

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            requestMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.recordRequest).toHaveBeenCalledWith(
                expect.any(Number),
                true,
                expect.objectContaining({
                    method: 'GET',
                    route: '/api/test',
                    userAgent: 'Mozilla/5.0',
                    statusCode: '200',
                    statusClass: '2xx',
                })
            );

            expect(mockMetricsCollector.recordTimer).toHaveBeenCalledWith(
                'http_request_duration_ms',
                expect.any(Number),
                expect.objectContaining({
                    method: 'GET',
                    route: '/api/test',
                    userAgent: 'Mozilla/5.0',
                })
            );

            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'http_requests_total',
                1,
                expect.objectContaining({
                    method: 'GET',
                    route: '/api/test',
                    status: '200',
                })
            );
        });

        it('should record error metrics on failed response', () => {
            mockResponse.statusCode = 500;
            const mockGet = mockRequest.get as jest.MockedFunction<any>;
            mockGet.mockReturnValue('Mozilla/5.0 Chrome/91.0');

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            requestMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.recordRequest).toHaveBeenCalledWith(
                expect.any(Number),
                false,
                expect.objectContaining({
                    statusCode: '500',
                    statusClass: '5xx',
                })
            );
        });

        it('should record slow request metrics', () => {
            const mockGet = mockRequest.get as jest.MockedFunction<any>;
            mockGet.mockReturnValue('Mozilla/5.0 Chrome/91.0');

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            // Mock Date.now to simulate slow request
            const originalDateNow = Date.now;
            let callCount = 0;
            Date.now = jest.fn(() => {
                callCount++;
                return callCount === 1 ? 1000 : 2500; // 1.5 second duration
            });

            requestMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'http_slow_requests_total',
                1,
                expect.objectContaining({
                    method: 'GET',
                    route: '/api/test',
                })
            );

            // Restore Date.now
            Date.now = originalDateNow;
        });

        it('should record response size when Content-Length is available', () => {
            const mockGet = mockRequest.get as jest.MockedFunction<any>;
            mockGet.mockReturnValue('Mozilla/5.0 Chrome/91.0');

            const mockResponseGet = mockResponse.get as jest.MockedFunction<any>;
            mockResponseGet.mockReturnValue('1024');

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            requestMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.recordHistogram).toHaveBeenCalledWith(
                'http_response_size_bytes',
                1024,
                expect.objectContaining({
                    method: 'GET',
                    route: '/api/test',
                })
            );
        });

        it('should handle response errors', () => {
            const mockGet = mockRequest.get as jest.MockedFunction<any>;
            mockGet.mockReturnValue('Mozilla/5.0 Chrome/91.0');

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let errorCallback: (error: Error) => void;

            mockOn.mockImplementation((event: string, callback: (error?: Error) => void) => {
                if (event === 'error') {
                    errorCallback = callback;
                }
            });

            requestMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate response error
            const testError = new Error('Test error');
            testError.name = 'TestError';
            errorCallback!(testError);

            expect(mockMetricsCollector.recordRequest).toHaveBeenCalledWith(
                expect.any(Number),
                false,
                expect.objectContaining({
                    error: 'TestError',
                })
            );

            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'http_request_errors_total',
                1,
                expect.objectContaining({
                    error: 'TestError',
                })
            );
        });

        it('should handle missing User-Agent header', () => {
            const mockGet = mockRequest.get as jest.MockedFunction<any>;
            mockGet.mockReturnValue(undefined);

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            requestMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.recordRequest).toHaveBeenCalledWith(
                expect.any(Number),
                true,
                expect.objectContaining({
                    userAgent: 'unknown',
                })
            );
        });

        it('should handle missing route information', () => {
            mockRequest.route = undefined;

            const mockGet = mockRequest.get as jest.MockedFunction<any>;
            mockGet.mockReturnValue('Mozilla/5.0 Chrome/91.0');

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            requestMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.recordRequest).toHaveBeenCalledWith(
                expect.any(Number),
                true,
                expect.objectContaining({
                    route: '/api/test', // Should fall back to path
                })
            );
        });
    });

    describe('activeConnectionsMonitoring', () => {
        it('should track active connections', () => {
            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            activeConnectionsMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockMetricsCollector.recordGauge).toHaveBeenCalledWith(
                'http_active_connections',
                1
            );

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.recordGauge).toHaveBeenCalledWith(
                'http_active_connections',
                0
            );
        });

        it('should handle connection close events', () => {
            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let closeCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'close') {
                    closeCallback = callback;
                }
            });

            activeConnectionsMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate connection close
            closeCallback!();

            expect(mockMetricsCollector.recordGauge).toHaveBeenCalledWith(
                'http_active_connections',
                0
            );
        });
    });

    describe('userActivityMonitoring', () => {
        it('should record user activity when user is present', () => {
            userActivityMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'user_requests_total',
                1,
                {
                    userId: 'user-123',
                    method: 'GET',
                    endpoint: '/api/test',
                }
            );

            expect(mockMetricsCollector.recordGauge).toHaveBeenCalledWith(
                'user_last_activity',
                expect.any(Number),
                {
                    userId: 'user-123',
                }
            );
        });

        it('should not record activity when user is not present', () => {
            delete mockRequest.user;

            userActivityMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockMetricsCollector.incrementCounter).not.toHaveBeenCalled();
            expect(mockMetricsCollector.recordGauge).not.toHaveBeenCalled();
        });

        it('should not record activity when userId is empty', () => {
            mockRequest.user = { id: '', email: '<EMAIL>', roles: [] };

            userActivityMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockMetricsCollector.incrementCounter).not.toHaveBeenCalled();
        });

        it('should not record activity when userId is whitespace only', () => {
            mockRequest.user = { id: '   ', email: '<EMAIL>', roles: [] };

            userActivityMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockMetricsCollector.incrementCounter).not.toHaveBeenCalled();
        });
    });

    describe('errorRateMonitoring', () => {
        it('should record client errors (4xx)', () => {
            mockResponse.statusCode = 404;

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            errorRateMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'http_errors_total',
                1,
                expect.objectContaining({
                    statusCode: '404',
                    errorType: 'client_error',
                })
            );

            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'http_not_found_total',
                1,
                expect.objectContaining({
                    method: 'GET',
                    route: '/api/test',
                })
            );
        });

        it('should record server errors (5xx)', () => {
            mockResponse.statusCode = 500;

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            errorRateMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'http_errors_total',
                1,
                expect.objectContaining({
                    statusCode: '500',
                    errorType: 'server_error',
                })
            );

            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'http_server_errors_total',
                1,
                expect.objectContaining({
                    method: 'GET',
                    route: '/api/test',
                })
            );
        });

        it('should record unauthorized errors (401)', () => {
            mockResponse.statusCode = 401;

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            errorRateMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'http_unauthorized_total',
                1,
                expect.objectContaining({
                    method: 'GET',
                    route: '/api/test',
                })
            );
        });

        it('should record forbidden errors (403)', () => {
            mockResponse.statusCode = 403;

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            errorRateMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'http_forbidden_total',
                1,
                expect.objectContaining({
                    method: 'GET',
                    route: '/api/test',
                })
            );
        });

        it('should not record metrics for successful responses', () => {
            mockResponse.statusCode = 200;

            const mockOn = mockResponse.on as jest.MockedFunction<any>;
            let finishCallback: () => void;

            mockOn.mockImplementation((event: string, callback: () => void) => {
                if (event === 'finish') {
                    finishCallback = callback;
                }
            });

            errorRateMonitoring(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            // Simulate response finish
            finishCallback!();

            expect(mockMetricsCollector.incrementCounter).not.toHaveBeenCalled();
        });
    });

    describe('monitorDatabaseOperation', () => {
        it('should monitor successful database operations', async () => {
            class TestService {
                @monitorDatabaseOperation('SELECT')
                async testMethod(): Promise<string> {
                    return 'success';
                }
            }

            const service = new TestService();
            const result = await service.testMethod();

            expect(result).toBe('success');
            expect(mockMetricsCollector.recordTimer).toHaveBeenCalledWith(
                'database_operation_duration_ms',
                expect.any(Number),
                {
                    operation: 'SELECT',
                    method: 'testMethod',
                }
            );
            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'database_operations_total',
                1,
                {
                    operation: 'SELECT',
                    method: 'testMethod',
                    status: 'success',
                }
            );
        });

        it('should monitor failed database operations', async () => {
            class TestService {
                @monitorDatabaseOperation('INSERT')
                async testMethod(): Promise<string> {
                    throw new Error('Database error');
                }
            }

            const service = new TestService();

            await expect(service.testMethod()).rejects.toThrow('Database error');

            expect(mockMetricsCollector.recordTimer).toHaveBeenCalledWith(
                'database_operation_duration_ms',
                expect.any(Number),
                {
                    operation: 'INSERT',
                    method: 'testMethod',
                }
            );
            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'database_operations_total',
                1,
                {
                    operation: 'INSERT',
                    method: 'testMethod',
                    status: 'error',
                }
            );
        });

        it('should monitor slow database operations', async () => {
            // Mock Date.now to simulate slow operation
            const originalDateNow = Date.now;
            let callCount = 0;
            Date.now = jest.fn(() => {
                callCount++;
                return callCount === 1 ? 1000 : 2500; // 1.5 second duration
            });

            class TestService {
                @monitorDatabaseOperation('UPDATE')
                async testMethod(): Promise<string> {
                    return 'success';
                }
            }

            const service = new TestService();
            await service.testMethod();

            expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                'database_slow_queries_total',
                1,
                {
                    operation: 'UPDATE',
                    method: 'testMethod',
                }
            );

            // Restore Date.now
            Date.now = originalDateNow;
        });
    });

    describe('BusinessMetrics', () => {
        describe('recordUserRegistration', () => {
            it('should record user registration with source', () => {
                BusinessMetrics.recordUserRegistration('web');

                expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                    'business_user_registrations_total',
                    1,
                    {
                        source: 'web',
                    }
                );
            });

            it('should record user registration without source', () => {
                BusinessMetrics.recordUserRegistration();

                expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                    'business_user_registrations_total',
                    1,
                    {
                        source: 'unknown',
                    }
                );
            });
        });

        describe('recordUserLogin', () => {
            it('should record successful user login', () => {
                BusinessMetrics.recordUserLogin('user-123', true);

                expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                    'business_user_logins_total',
                    1,
                    {
                        status: 'success',
                    }
                );

                expect(mockMetricsCollector.recordGauge).toHaveBeenCalledWith(
                    'business_user_last_login',
                    expect.any(Number),
                    {
                        userId: 'user-123',
                    }
                );
            });

            it('should record failed user login', () => {
                BusinessMetrics.recordUserLogin('user-123', false);

                expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                    'business_user_logins_total',
                    1,
                    {
                        status: 'failed',
                    }
                );

                expect(mockMetricsCollector.recordGauge).not.toHaveBeenCalled();
            });
        });

        describe('recordAgentCreation', () => {
            it('should record agent creation', () => {
                BusinessMetrics.recordAgentCreation('user-123');

                expect(mockMetricsCollector.incrementCounter).toHaveBeenCalledWith(
                    'business_agents_created_total',
                    1,
                    {
                        userId: 'user-123',
                    }
                );
            });
        });
    });
});
