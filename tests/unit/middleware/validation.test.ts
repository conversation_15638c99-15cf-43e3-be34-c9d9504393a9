import { Request, Response, NextFunction } from 'express';
import { validate, validateQuery, validateParams, validateBody } from '@/middleware/validation';
import { z } from 'zod/v4';
import { StatusCodes } from 'http-status-codes';
import type { AuthenticatedRequest, TypedResponse } from '@/types';

// Mock logger
jest.mock('@/config/logger', () => ({
    logError: jest.fn(),
}));

describe('Validation Middleware', () => {
    let mockRequest: Partial<AuthenticatedRequest>;
    let mockResponse: Partial<TypedResponse>;
    let mockNext: NextFunction;

    beforeEach(() => {
        mockRequest = {
            requestId: 'test-request-id',
            method: 'POST',
            url: '/api/test',
            query: {},
            params: {},
            body: {},
        };

        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis(),
        };

        mockNext = jest.fn();
        jest.clearAllMocks();
    });

    describe('validate', () => {
        const testSchema = z.object({
            name: z.string().min(1, 'Name is required'),
            age: z.number().min(0, 'Age must be non-negative'),
            email: z.string().email('Invalid email format').optional(),
        });

        it('should pass validation with valid data', () => {
            const validData = {
                name: 'John Doe',
                age: 25,
                email: '<EMAIL>',
            };

            mockRequest.body = validData;
            const middleware = validate(testSchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockResponse.status).not.toHaveBeenCalled();
        });

        it('should reject invalid data with validation errors', () => {
            const invalidData = {
                name: '',
                age: -5,
                email: 'invalid-email',
            };

            mockRequest.body = invalidData;
            const middleware = validate(testSchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: false,
                message: 'Validation failed',
                error: expect.stringContaining('Name is required'),
                timestamp: expect.any(String),
                requestId: 'test-request-id',
            });
            expect(mockNext).not.toHaveBeenCalled();
        });

        it('should handle missing data', () => {
            mockRequest.body = undefined;
            const middleware = validate(testSchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
            expect(mockNext).not.toHaveBeenCalled();
        });

        it('should transform data according to schema', () => {
            const dataWithStringNumbers = {
                name: 'John Doe',
                age: '25', // String that should be transformed to number
            };

            const transformSchema = z.object({
                name: z.string(),
                age: z.coerce.number(),
            });

            mockRequest.body = dataWithStringNumbers;
            const middleware = validate(transformSchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockRequest.body.age).toBe(25); // Should be transformed to number
        });

        it('should handle schema parsing errors', () => {
            const invalidSchema = z.object({
                requiredField: z.string(),
            });

            mockRequest.body = { optionalField: 'value' };
            const middleware = validate(invalidSchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
            expect(mockNext).not.toHaveBeenCalled();
        });
    });

    describe('validateQuery', () => {
        const querySchema = z.object({
            page: z.coerce.number().min(1).default(1),
            limit: z.coerce.number().min(1).max(100).default(10),
            search: z.string().optional(),
        });

        it('should validate query parameters successfully', () => {
            mockRequest.query = {
                page: '2',
                limit: '20',
                search: 'test',
            };

            const middleware = validateQuery(querySchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockRequest.query).toEqual({
                page: 2,
                limit: 20,
                search: 'test',
            });
        });

        it('should apply default values for missing query parameters', () => {
            mockRequest.query = {};

            const middleware = validateQuery(querySchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockRequest.query).toEqual({
                page: 1,
                limit: 10,
            });
        });

        it('should reject invalid query parameters', () => {
            mockRequest.query = {
                page: '0', // Invalid: less than 1
                limit: '101', // Invalid: greater than 100
            };

            const middleware = validateQuery(querySchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
            expect(mockNext).not.toHaveBeenCalled();
        });
    });

    describe('validateParams', () => {
        const paramsSchema = z.object({
            id: z.uuid('Invalid ID format'),
            type: z.enum(['user', 'admin']).optional(),
        });

        it('should validate URL parameters successfully', () => {
            mockRequest.params = {
                id: '550e8400-e29b-41d4-a716-************',
                type: 'user',
            };

            const middleware = validateParams(paramsSchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockRequest.params).toEqual({
                id: '550e8400-e29b-41d4-a716-************',
                type: 'user',
            });
        });

        it('should reject invalid UUID parameter', () => {
            mockRequest.params = {
                id: 'invalid-uuid',
            };

            const middleware = validateParams(paramsSchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: false,
                message: 'Validation failed',
                error: expect.stringContaining('Invalid ID format'),
                timestamp: expect.any(String),
                requestId: 'test-request-id',
            });
            expect(mockNext).not.toHaveBeenCalled();
        });

        it('should reject invalid enum parameter', () => {
            mockRequest.params = {
                id: '550e8400-e29b-41d4-a716-************',
                type: 'invalid-type',
            };

            const middleware = validateParams(paramsSchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
            expect(mockNext).not.toHaveBeenCalled();
        });
    });

    describe('validateBody', () => {
        const bodySchema = z.object({
            title: z.string().min(1, 'Title is required'),
            content: z.string().optional(),
            tags: z.array(z.string()).default([]),
            published: z.boolean().default(false),
        });

        it('should validate request body successfully', () => {
            mockRequest.body = {
                title: 'Test Article',
                content: 'This is test content',
                tags: ['test', 'article'],
                published: true,
            };

            const middleware = validateBody(bodySchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockRequest.body).toEqual({
                title: 'Test Article',
                content: 'This is test content',
                tags: ['test', 'article'],
                published: true,
            });
        });

        it('should apply default values for missing body fields', () => {
            mockRequest.body = {
                title: 'Test Article',
            };

            const middleware = validateBody(bodySchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockNext).toHaveBeenCalled();
            expect(mockRequest.body).toEqual({
                title: 'Test Article',
                tags: [],
                published: false,
            });
        });

        it('should reject invalid body data', () => {
            mockRequest.body = {
                title: '', // Invalid: empty string
                tags: 'not-an-array', // Invalid: should be array
            };

            const middleware = validateBody(bodySchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
            expect(mockNext).not.toHaveBeenCalled();
        });

        it('should handle empty request body', () => {
            mockRequest.body = undefined;

            const middleware = validateBody(bodySchema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
            expect(mockNext).not.toHaveBeenCalled();
        });
    });

    describe('error handling', () => {
        it('should log validation errors', () => {
            const { logError } = require('@/config/logger');
            const schema = z.object({
                required: z.string(),
            });

            mockRequest.body = {};
            const middleware = validate(schema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(logError).toHaveBeenCalledWith(
                'Validation failed',
                {
                    requestId: 'test-request-id',
                    method: 'POST',
                    url: '/api/test',
                    validationErrors: expect.any(Array),
                },
                expect.any(Error)
            );
        });

        it('should handle missing requestId gracefully', () => {
            delete mockRequest.requestId;
            const schema = z.object({
                required: z.string(),
            });

            mockRequest.body = {};
            const middleware = validate(schema);

            middleware(
                mockRequest as AuthenticatedRequest,
                mockResponse as TypedResponse,
                mockNext
            );

            expect(mockResponse.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: false,
                message: 'Validation failed',
                error: expect.any(String),
                timestamp: expect.any(String),
                requestId: undefined,
            });
        });
    });
});
