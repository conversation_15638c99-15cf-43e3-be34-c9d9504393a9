// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    // eslint-disable-next-line no-console
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', error => {
    // eslint-disable-next-line no-console
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
