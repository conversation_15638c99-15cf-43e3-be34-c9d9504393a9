{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2022"], "types": ["jest", "node"], "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/config/*": ["src/config/*"], "@/controllers/*": ["src/controllers/*"], "@/middleware/*": ["src/middleware/*"], "@/models/*": ["src/models/*"], "@/routes/*": ["src/routes/*"], "@/services/*": ["src/services/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"], "@/db/*": ["src/db/*"]}}, "include": ["src/**/*", "scripts/**/*", "drizzle.config.ts"], "exclude": ["node_modules", "dist", "tests/**/*", "examples/**/*"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}