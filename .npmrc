# 默认使用npm官方源
registry=https://registry.npmjs.org/

# 特定scope的包使用私服
# 例如：@your-company scope的包使用私服
# @your-company:registry=https://your-private-registry.com/
# @internal:registry=https://npm.internal.com/
# @private:registry=https://registry.private.com/

# 或者特定包名使用私服
# your-private-package:registry=https://your-private-registry.com/
# some-internal-lib:registry=https://npm.internal.com/
# company-utils:registry=https://registry.company.com/
@xui-web-app:registry=https://npm.yunxuetang.com.cn/repository/yxt-npm-internal/

# 如果私服需要认证，可以配置token
# //your-private-registry.com/:_authToken=your-token-here
# //npm.internal.com/:_authToken=${NPM_INTERNAL_TOKEN}
# //registry.company.com/:username=your-username
# //registry.company.com/:_password=your-base64-password
# //registry.company.com/:email=<EMAIL>
