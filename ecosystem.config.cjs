/**
 * PM2 生态系统配置文件
 * 用于生产环境的进程管理
 * 使用 tsx 运行 TypeScript ES 模块
 */

module.exports = {
    apps: [
        {
            // 应用基本信息
            name: 'xui-app-server',
            script: 'npx',
            args: 'tsx src/index.ts',

            // 实例配置
            instances: 1, // 单实例模式，适合 TypeScript
            exec_mode: 'fork', // fork 模式，适合 TypeScript

            // 环境变量 (统一配置)
            env: {
                NODE_ENV: 'development',
            },

            // 日志配置
            log_file: './logs/combined.log',
            out_file: './logs/out.log',
            error_file: './logs/error.log',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            merge_logs: true,

            // 进程管理
            pid_file: './pids/app.pid',
            restart_delay: 4000, // 重启延迟
            max_restarts: 10, // 最大重启次数
            min_uptime: '10s', // 最小运行时间

            // 内存和CPU限制
            max_memory_restart: '500M', // 内存超过500MB时重启

            // 监控配置
            pmx: true,

            // 自动重启配置
            watch: false, // 生产环境不监听文件变化
            ignore_watch: ['node_modules', 'logs', 'pids', '.git', 'tests', 'docs'],

            // 健康检查
            health_check_grace_period: 3000,

            // 优雅关闭
            kill_timeout: 5000,
            listen_timeout: 3000,

            // 集群配置
            instance_var: 'INSTANCE_ID',

            // 源码映射支持
            source_map_support: true,

            // 自定义启动脚本
            node_args: [
                '--max-old-space-size=512', // 限制老生代内存
                '--optimize-for-size', // 优化内存使用
            ],

            // 自动部署配置
            deploy: {
                production: {
                    user: process.env.DEPLOY_USER || 'deploy',
                    host: process.env.DEPLOY_HOST || 'localhost',
                    ref: 'origin/main',
                    repo:
                        process.env.DEPLOY_REPO ||
                        '**************:your-org/xui-app-server.git',
                    path: process.env.DEPLOY_PATH || '/var/www/xui-app-server',
                    'pre-deploy-local': '',
                    'post-deploy':
                        'pnpm install --frozen-lockfile && pnpm build && pm2 reload ecosystem.config.cjs',
                    'pre-setup': '',
                    ssh_options: 'StrictHostKeyChecking=no',
                },
            },
        },
    ],

    // PM2+ 监控配置
    pmx: {
        http: true, // 启用 HTTP 监控
        ignore_routes: ['/health'], // 忽略健康检查路由
        errors: true, // 错误监控
        custom_probes: true, // 自定义探针
        network: true, // 网络监控
        ports: true, // 端口监控
    },
};
