# XUI App Server - 开发环境配置模板
# 复制此文件为 .env.development 并根据需要修改

# =============================================================================
# 基础配置
# =============================================================================
PORT=3000
HOST=0.0.0.0

# =============================================================================
# 数据库配置 (PostgreSQL)
# =============================================================================
# DB_HOST=*************
DB_HOST=***********
DB_PORT=5432
DB_NAME=xui
DB_USER=ai
DB_PASSWORD=WyaJCZZb93AvlL6RA7r8
DB_SSL=false
DB_MAX_CONNECTIONS=10
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=2000

# =============================================================================
# 安全配置
# =============================================================================
# 注意：本项目使用网关认证，通过 userId header 传递用户信息
# HSTS_ENABLED 在产品环境服务器上开启，默认不开启
BCRYPT_ROUNDS=8
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

HSTS_ENABLED=false
HSTS_MAX_AGE=31536000 # (1 year)

# =============================================================================
# CORS 配置
# =============================================================================
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# =============================================================================
# 日志配置
# =============================================================================
LOG_DIR=logs    # 日志目录
LOG_LEVEL=debug # 开发环境推荐使用 debug 级别
LOG_FORMAT=console  # 开发环境推荐使用 console 格式, 正式环境使用 json 格式

# =============================================================================
# Langfuse 配置 (LLM 可观测性) - 开发环境
# =============================================================================
LANGFUSE_PUBLIC_KEY=pk-lf-7ec26f80-c670-4130-a149-4ba38a4f1313
LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_HOST=https://langfuse.yxt.com.cn

# =============================================================================
# Redis 配置 (用于 SSE 会话管理和消息缓存)
# =============================================================================
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=iN8S1$uPNTEUR52m
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000
REDIS_CONNECTION_TIMEOUT=5000
REDIS_COMMAND_TIMEOUT=5000
REDIS_KEY_PREFIX=xui:
REDIS_SESSION_TTL=3600
REDIS_MESSAGE_CACHE_SIZE=100

# =============================================================================
# nacos 配置
# =============================================================================
NACOS_ENABLED=false
NACOS_SERVER_ADDR=
NACOS_NAMESPACE=
NACOS_USERNAME=
NACOS_PASSWORD=
NACOS_DATA_ID=app-config
NACOS_GROUP=DEFAULT_GROUP

# =============================================================================
# 注意事项
# =============================================================================
# 1. 开发环境配置不应用于生产环境
# 2. 密码可以使用简单值，但不要提交到版本控制
# 3. 确保本地数据库服务正在运行
# 4. 确保 Redis 服务正在运行（用于 SSE 会话管理）
