import { config } from 'dotenv';
import { defineConfig } from 'drizzle-kit';
import { existsSync } from 'fs';

// Load single environment config file (.env)
const envFile = '.env';

if (existsSync(envFile)) {
    config({ path: envFile });
    // eslint-disable-next-line no-console
    console.log(`✓ Loaded environment config: ${envFile}`);
} else {
    // eslint-disable-next-line no-console
    console.warn(`⚠️  No environment file found: ${envFile}`);
}

export default defineConfig({
    dialect: 'postgresql',
    schema: './src/infrastructure/database/schema/*',
    out: './drizzle',
    dbCredentials: {
        host: process.env["DB_HOST"]!,
        port: Number(process.env["DB_PORT"]!),
        user: process.env["DB_USER"]!,
        password: process.env["DB_PASSWORD"]!,
        database: process.env["DB_NAME"]!,
        ssl: process.env["DB_SSL"] === 'true',
    },
    // Print all statements
    verbose: true,
    // Always ask for confirmation
    strict: true,
    migrations: {
        table: 'migrations',
        schema: 'drizzle',
    },
});
