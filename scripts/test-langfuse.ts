/**
 * Langfuse Standalone Test Script
 * 
 * This script is designed to test the Langfuse integration in isolation,
 * without running the full application server. It helps verify that
 * credentials are correct, the client can connect, and data can be sent.
 * 
 * To run this test:
 * 1. Ensure your .env file has the correct LANGFUSE_* variables.
 * 2. Run the command: `pnpm test:langfuse`
 */

// Must be imported first to enable dependency injection
import 'reflect-metadata';

// Load environment variables from .env file
import 'dotenv/config';

import getLangfuseService from '@/infrastructure/logger/langfuse';
import { Logger } from '@/infrastructure/logger';

async function testLangfuseIntegration(): Promise<void> {
    Logger.info('--- Starting Langfuse Standalone Test ---');

    const langfuse = getLangfuseService();

    // 1. Check if the Langfuse service is enabled
    if (!langfuse.isEnabled()) {
        Logger.error('Langfuse is not enabled or failed to initialize.');
        Logger.error('Please check your .env file for the following variables:');
        Logger.error(' - LANGFUSE_PUBLIC_KEY');
        Logger.error(' - LANGFUSE_SECRET_KEY');
        Logger.info('--- Test Failed ---');
        return;
    }

    Logger.info('Langfuse is enabled. Proceeding with trace creation.');

    try {
        // 2. Create a root trace
        const trace = langfuse.createTrace(
            'Standalone Test Trace',
            { testInput: 'some value' },
            {
                sessionId: `test-session-${Date.now()}`,
                userId: 'test-user-123',
                release: '1.0.0',
            }
        );

        if (!trace) {
            Logger.error('Failed to create a Langfuse trace object. The client might be null.');
            Logger.info('--- Test Failed ---');
            return;
        }

        Logger.info(`Trace created with ID: ${trace.id}`);

        // 3. Create a child event within the trace
        trace.event({
            name: 'Test Event',
            input: { detail: 'This is a test event from the standalone script.' },
            output: { success: true },
        });

        Logger.info('Event created within the trace.');
        
    } catch (error) {
        Logger.error('An error occurred during trace creation:', {}, error as Error);
        Logger.info('--- Test Failed ---');
        // Ensure shutdown is called even on error
        await langfuse.shutdown();
        return;
    }

    // 5. Crucially, shut down the client to send all buffered data
    try {
        Logger.info('Shutting down Langfuse client to send data...');
        await langfuse.shutdown();
        Logger.info('Langfuse client shut down successfully.');
        Logger.info('Check your Langfuse project to see if the trace "Standalone Test Trace" appeared.');
        Logger.info('--- Test Succeeded ---');
    } catch (error) {
        Logger.error('An error occurred during Langfuse shutdown:', {}, error as Error);
        Logger.info('--- Test Failed ---');
    }
}

// Run the test
void testLangfuseIntegration(); 