import 'dotenv/config';
import 'reflect-metadata';
import { getEnvironment, initializeNacosConfig } from '@/shared/utils/env';

async function validateEnvironment(): Promise<void> {
    try {
        // 首先初始化Nacos配置（如果配置了的话）
        await initializeNacosConfig();

        // 获取最新的环境配置（包含Nacos配置）
        const env = getEnvironment();


        console.log('✓ 环境配置验证通过');
        console.log(`  - 端口: ${env.PORT}`);
        console.log(`  - 主机: ${env.HOST}`);
        console.log(`  - 数据库: ${env.DB_HOST}:${env.DB_PORT}/${env.DB_NAME}`);
        
        // 检查Nacos配置
        if (env.NACOS_SERVER_ADDR) {
            console.log('✓ Nacos配置已启用');
            console.log(`  - 服务器: ${env.NACOS_SERVER_ADDR}`);
            console.log(`  - 命名空间: ${env.NACOS_NAMESPACE ?? 'public'}`);
            console.log(`  - Data ID: ${env.NACOS_DATA_ID}`);
            console.log(`  - Group: ${env.NACOS_GROUP}`);
        } else {
            console.log('ℹ Nacos配置未启用，使用本地环境变量');
        }

        // 检查可选配置
        if (env.REDIS_URL) {
            console.log(`  - Redis: ${env.REDIS_URL}`);
        }

        if (env.ENABLE_METRICS) {
            console.log(`  - 指标端口: ${env.METRICS_PORT}`);
        }

        process.exit(0);
    } catch (error) {
        if (error instanceof Error) {
            console.error('❌ 环境配置验证失败:', error.message);
        } else {
            console.error('❌ 环境配置验证失败:', error);
        }
        process.exit(1);
    }
}

// 运行验证
void validateEnvironment(); 