#!/bin/bash

# XUI App Server - 个人快速部署脚本 (PM2 模式)
# 用于在本地或个人服务器上快速部署应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
XUI App Server 个人快速部署脚本 (PM2 模式)

用法: $0 [选项]

此脚本用于在本地或个人服务器上快速部署应用。
它假定 .env 配置文件已手动创建，并且不执行数据库迁移。

选项:
  --skip-deps     跳过依赖安装
  --skip-build    跳过构建步骤
  --stop-only     仅停止现有服务，不重新部署
  --force-stop    强制停止所有相关服务 (同 --stop-only)
  --help          显示此帮助信息

示例:
  $0                # 完整部署应用
  $0 --skip-build   # 部署应用，但跳过构建步骤
  $0 --stop-only    # 仅停止所有 PM2 服务
EOF
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装。请安装 Node.js 20.0.0 或更高版本"
        exit 1
    fi
    
    local node_version=$(node -v | cut -d'v' -f2)
    local required_version="20.0.0"
    
    if ! printf '%s\n%s\n' "$required_version" "$node_version" | sort -V -C; then
        log_error "Node.js 版本过低。当前版本: $node_version，要求: $required_version+"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        log_warning "pnpm 未安装，正在安装..."
        npm install -g pnpm
    fi
    
    log_success "系统要求检查完成"
}

# 安装依赖
install_dependencies() {
    if [ "$SKIP_DEPS" = "true" ]; then
        log_info "跳过依赖安装"
        return
    fi
    
    log_info "安装依赖..."
    pnpm install
    log_success "依赖安装完成"
}

# 构建应用
build_application() {
    if [ "$SKIP_BUILD" = "true" ]; then
        log_info "跳过构建步骤"
        return
    fi
    
    log_info "构建应用..."
    pnpm build
    log_success "应用构建完成"
}

# 停止 PM2 服务
stop_pm2_service() {
    log_info "停止 PM2 服务..."

    # 检查 PM2 是否安装
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 未安装，无法停止服务"
        return
    fi
    
    # 尝试停止并删除现有的进程，即使它不存在也不会报错
    log_info "正在清理旧的 'xui-app-server' PM2 进程（如果存在）..."
    pm2 stop xui-app-server --silent || true
    pm2 delete xui-app-server --silent || true
    log_success "旧进程清理完成。"
}

# PM2 部署
deploy_pm2() {
    log_info "使用 PM2 部署..."

    # 检查 PM2 是否安装
    if ! command -v pm2 &> /dev/null; then
        log_info "安装 PM2..."
        npm install -g pm2
    fi

    # 停止现有应用
    stop_pm2_service

    # 启动应用 (不再需要 --env)
    log_info "启动 PM2 应用..."
    pm2 start ecosystem.config.cjs
    pm2 save

    log_success "PM2 部署完成"
}

# 从环境文件中获取端口
get_port_from_env() {
    local env_file=".env"
    if [ -f "$env_file" ]; then
        # 从 .env 文件中提取 PORT 值
        local port_val=$(grep -E '^PORT=' "$env_file" | cut -d'=' -f2 | tr -d '[:space:]')
        if [ -n "$port_val" ]; then
            APP_PORT=$port_val
            log_info "从 $env_file 中检测到端口: $APP_PORT"
            return
        fi
    fi
    # 如果未找到，使用默认值
    APP_PORT=3000
    log_info "未在配置文件中找到端口，使用默认端口: $APP_PORT"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        # 尝试简单的 ping 端点，更快更可靠
        if curl -f http://localhost:$APP_PORT/ping >/dev/null 2>&1; then
            log_success "健康检查通过"
            return 0
        fi

        log_info "健康检查失败，重试 $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done
    
    log_error "健康检查失败"
    return 1
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "部署方式: PM2"
    echo "应用地址: http://localhost:$APP_PORT"
    echo "健康检查: http://localhost:$APP_PORT/ping"
    echo "详细健康检查: http://localhost:$APP_PORT/health"
    echo
    
    echo "PM2 管理命令:"
    echo "  查看状态: pm2 status"
    echo "  查看日志: pm2 logs xui-app-server"
    echo "  重启应用: pm2 restart xui-app-server"
    echo "  停止应用: pm2 stop xui-app-server"
}

# 主函数
main() {
    # 默认值
    STOP_ONLY=false
    FORCE_STOP=false
    SKIP_DEPS=false
    SKIP_BUILD=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --stop-only)
                STOP_ONLY=true
                shift
                ;;
            --force-stop)
                FORCE_STOP=true
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果是停止服务
    if [ "$STOP_ONLY" = "true" ] || [ "$FORCE_STOP" = "true" ]; then
        stop_pm2_service
        log_success "服务已停止"
        exit 0
    fi

    # 获取端口号
    get_port_from_env

    log_info "开始 PM2 快速部署..."
    
    # 执行部署步骤
    check_requirements
    install_dependencies
    build_application
    deploy_pm2
    
    # 健康检查
    sleep 5
    if health_check; then
        show_deployment_info
    else
        log_error "部署可能存在问题，请检查日志"
        exit 1
    fi
}

# 运行主函数
main "$@"
